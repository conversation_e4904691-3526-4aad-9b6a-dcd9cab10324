package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yi.controller.user.model.ResourceVO;
import com.yi.entity.TResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资源表 Mapper 接口
 */
@Mapper
public interface TResourceMapper extends BaseMapper<TResource> {

    /**
     * 查询所有资源（树形结构）
     *
     * @param resourceType 资源类型
     * @param status 状态
     * @return 资源列表
     */
    List<TResource> selectResourceTree(@Param("resourceType") Integer resourceType,
                                       @Param("status") Integer status);

    /**
     * 根据父ID查询子资源
     *
     * @param parentId 父资源ID
     * @return 资源列表
     */
    List<TResource> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 根据资源编码查询资源
     *
     * @param resourceCode 资源编码
     * @return 资源信息
     */
    TResource selectByResourceCode(@Param("resourceCode") String resourceCode);

    /**
     * 根据用户ID查询用户拥有的资源
     *
     * @param userId 用户ID
     * @param resourceType 资源类型
     * @return 资源列表
     */
    List<TResource> selectByUserId(@Param("userId") Long userId,
                                   @Param("resourceType") Integer resourceType);

    /**
     * 根据角色ID查询角色拥有的资源
     *
     * @param roleId 角色ID
     * @return 资源列表
     */
    List<TResource> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 查询用户的菜单权限
     *
     * @param userId 用户ID
     * @return 菜单资源列表
     */
    List<TResource> selectMenusByUserId(@Param("userId") Long userId);

    /**
     * 查询用户的按钮权限
     *
     * @param userId 用户ID
     * @return 按钮资源列表
     */
    List<TResource> selectButtonsByUserId(@Param("userId") Long userId);

    /**
     * 查询所有启用的资源
     *
     * @return 资源列表
     */
    List<TResource> selectEnabledResources();

    /**
     * 检查资源是否有子资源
     *
     * @param resourceId 资源ID
     * @return 是否有子资源
     */
    boolean hasChildren(@Param("resourceId") Long resourceId);


    /**
     * 根据角色ID查询角色拥有的资源
     *
     * @param roleIdList 角色ID
     * @return 资源列表
     */
    List<ResourceVO> selectByRoleIdList(@Param("roleIdList") List<Long> roleIdList);

}
