-- =============================================
-- 出入库管理系统数据库表结构
-- =============================================

-- 1. 出库单表
DROP TABLE IF EXISTS `t_outbound_order`;
CREATE TABLE `t_outbound_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(50) NOT NULL COMMENT '出库单号（F+年月日+递增序列号）',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '出库状态：1-待出库，2-运输中，3-已出库',
  `outbound_type` int(11) NOT NULL COMMENT '出库类型：1-销售出库，2-调拨出库',
  `outbound_company_id` bigint(20) DEFAULT NULL COMMENT '出库公司ID',
  `outbound_company_name` varchar(200) DEFAULT NULL COMMENT '出库公司名称',
  `outbound_address` varchar(500) DEFAULT NULL COMMENT '出库地址',
  `delivery_method` varchar(100) DEFAULT NULL COMMENT '配送方式',
  `vehicle_number` varchar(50) DEFAULT NULL COMMENT '车牌号',
  `driver_name` varchar(100) DEFAULT NULL COMMENT '司机姓名',
  `driver_phone` varchar(20) DEFAULT NULL COMMENT '司机联系方式',
  `receive_company_id` bigint(20) DEFAULT NULL COMMENT '收货公司ID',
  `receive_company_name` varchar(200) DEFAULT NULL COMMENT '收货公司名称',
  `receive_address` varchar(500) DEFAULT NULL COMMENT '收货地址',
  `first_category` int(11) DEFAULT NULL COMMENT '一级类目：1-共享托盘',
  `second_category` varchar(100) DEFAULT NULL COMMENT '二级类目',
  `planned_quantity` int(11) DEFAULT '0' COMMENT '计划出库数',
  `actual_quantity` int(11) DEFAULT '0' COMMENT '实际出库数',
  `outbound_time` datetime DEFAULT NULL COMMENT '出库时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
  `last_modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_status` (`status`),
  KEY `idx_outbound_type` (`outbound_type`),
  KEY `idx_outbound_company_id` (`outbound_company_id`),
  KEY `idx_receive_company_id` (`receive_company_id`),
  KEY `idx_first_category` (`first_category`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_valid` (`valid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库单表';

-- 2. 入库单表
DROP TABLE IF EXISTS `t_inbound_order`;
CREATE TABLE `t_inbound_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(50) NOT NULL COMMENT '入库单号（与出库单号一致）',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '入库状态：1-待入库，2-部分入库，3-已入库',
  `inbound_type` int(11) NOT NULL COMMENT '入库类型：1-采购入库，2-回收入库，3-调拨入库，4-销售入库',
  `inbound_warehouse_id` bigint(20) DEFAULT NULL COMMENT '入库仓库ID',
  `inbound_warehouse_name` varchar(200) DEFAULT NULL COMMENT '入库仓库名称',
  `delivery_method` varchar(100) DEFAULT NULL COMMENT '配送方式',
  `vehicle_number` varchar(50) DEFAULT NULL COMMENT '车牌号',
  `driver_name` varchar(100) DEFAULT NULL COMMENT '司机姓名',
  `driver_phone` varchar(20) DEFAULT NULL COMMENT '司机联系方式',
  `sender_warehouse_id` bigint(20) DEFAULT NULL COMMENT '发货仓库ID',
  `sender_warehouse_name` varchar(200) DEFAULT NULL COMMENT '发货仓库名称',
  `sender_address` varchar(500) DEFAULT NULL COMMENT '发货地址',
  `first_category` int(11) DEFAULT NULL COMMENT '一级类目：1-共享托盘',
  `second_category` varchar(100) DEFAULT NULL COMMENT '二级类目',
  `planned_quantity` int(11) DEFAULT '0' COMMENT '计划入库数',
  `actual_quantity` int(11) DEFAULT '0' COMMENT '实际入库数',
  `inbound_time` datetime DEFAULT NULL COMMENT '入库时间',
  `outbound_order_id` bigint(20) DEFAULT NULL COMMENT '关联出库单ID',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
  `last_modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_status` (`status`),
  KEY `idx_inbound_type` (`inbound_type`),
  KEY `idx_inbound_warehouse_id` (`inbound_warehouse_id`),
  KEY `idx_sender_warehouse_id` (`sender_warehouse_id`),
  KEY `idx_first_category` (`first_category`),
  KEY `idx_outbound_order_id` (`outbound_order_id`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_valid` (`valid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入库单表';

-- 3. 单号序列表（用于生成递增序列号）
DROP TABLE IF EXISTS `t_order_sequence`;
CREATE TABLE `t_order_sequence` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `date_key` varchar(8) NOT NULL COMMENT '日期键（YYYYMMDD）',
  `sequence_value` int(11) NOT NULL DEFAULT '0' COMMENT '当日序列号',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_key` (`date_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单号序列表';

-- 4. 出库单明细表
DROP TABLE IF EXISTS `t_outbound_order_detail`;
CREATE TABLE `t_outbound_order_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `outbound_order_id` bigint(20) NOT NULL COMMENT '出库单ID',
  `planned_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '计划出库数量',
  `actual_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '实际出库数量',
  `rollback_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '回退数量',
  `source_inbound_order_id` bigint(20) DEFAULT NULL COMMENT '来源入库单ID',
  `source_inbound_order_no` varchar(50) DEFAULT NULL COMMENT '来源入库单号',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
  `last_modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_outbound_order_id` (`outbound_order_id`),
  KEY `idx_source_inbound_order_id` (`source_inbound_order_id`),
  KEY `idx_source_inbound_order_no` (`source_inbound_order_no`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_valid` (`valid`),
  CONSTRAINT `fk_outbound_detail_outbound_order` FOREIGN KEY (`outbound_order_id`) REFERENCES `t_outbound_order` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_outbound_detail_source_inbound_order` FOREIGN KEY (`source_inbound_order_id`) REFERENCES `t_inbound_order` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出库单明细表';

-- 5. 入库单明细表
DROP TABLE IF EXISTS `t_inbound_order_detail`;
CREATE TABLE `t_inbound_order_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `inbound_order_id` bigint(20) NOT NULL COMMENT '入库单ID',
  `planned_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '计划入库数量',
  `actual_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '实际入库数量',
  `rollback_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '回退数量',
  `source_inbound_order_id` bigint(20) DEFAULT NULL COMMENT '来源入库单ID（用于调拨等场景）',
  `source_inbound_order_no` varchar(50) DEFAULT NULL COMMENT '来源入库单号',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
  `last_modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_inbound_order_id` (`inbound_order_id`),
  KEY `idx_source_inbound_order_id` (`source_inbound_order_id`),
  KEY `idx_source_inbound_order_no` (`source_inbound_order_no`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_valid` (`valid`),
  CONSTRAINT `fk_inbound_detail_inbound_order` FOREIGN KEY (`inbound_order_id`) REFERENCES `t_inbound_order` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_inbound_detail_source_inbound_order` FOREIGN KEY (`source_inbound_order_id`) REFERENCES `t_inbound_order` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入库单明细表';

-- 6. 库存流水表（可选，用于追踪库存变化）
DROP TABLE IF EXISTS `t_inventory_transaction`;
CREATE TABLE `t_inventory_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `transaction_type` int(11) NOT NULL COMMENT '交易类型：1-入库，2-出库，3-调拨，4-盘点',
  `order_id` bigint(20) DEFAULT NULL COMMENT '关联订单ID',
  `order_no` varchar(50) DEFAULT NULL COMMENT '关联订单号',
  `detail_id` bigint(20) DEFAULT NULL COMMENT '关联明细ID',
  `warehouse_id` bigint(20) DEFAULT NULL COMMENT '仓库ID',
  `warehouse_name` varchar(200) DEFAULT NULL COMMENT '仓库名称',
  `first_category` int(11) DEFAULT NULL COMMENT '一级类目',
  `second_category` varchar(100) DEFAULT NULL COMMENT '二级类目',
  `quantity_before` int(11) NOT NULL DEFAULT '0' COMMENT '变更前数量',
  `quantity_change` int(11) NOT NULL DEFAULT '0' COMMENT '变更数量（正数为增加，负数为减少）',
  `quantity_after` int(11) NOT NULL DEFAULT '0' COMMENT '变更后数量',
  `transaction_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '交易时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_detail_id` (`detail_id`),
  KEY `idx_warehouse_id` (`warehouse_id`),
  KEY `idx_first_category` (`first_category`),
  KEY `idx_transaction_time` (`transaction_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存流水表';

-- 7. 创建索引优化查询性能
-- 出库单复合索引
CREATE INDEX `idx_outbound_status_type` ON `t_outbound_order` (`status`, `outbound_type`);
CREATE INDEX `idx_outbound_time_range` ON `t_outbound_order` (`outbound_time`, `status`);
CREATE INDEX `idx_outbound_company_time` ON `t_outbound_order` (`outbound_company_id`, `created_time`);

-- 入库单复合索引
CREATE INDEX `idx_inbound_status_type` ON `t_inbound_order` (`status`, `inbound_type`);
CREATE INDEX `idx_inbound_time_range` ON `t_inbound_order` (`inbound_time`, `status`);
CREATE INDEX `idx_inbound_warehouse_time` ON `t_inbound_order` (`inbound_warehouse_id`, `created_time`);

-- 出库单明细复合索引
CREATE INDEX `idx_outbound_detail_order_source` ON `t_outbound_order_detail` (`outbound_order_id`, `source_inbound_order_id`);
CREATE INDEX `idx_outbound_detail_valid_time` ON `t_outbound_order_detail` (`valid`, `created_time`);

-- 入库单明细复合索引
CREATE INDEX `idx_inbound_detail_order_source` ON `t_inbound_order_detail` (`inbound_order_id`, `source_inbound_order_id`);
CREATE INDEX `idx_inbound_detail_valid_time` ON `t_inbound_order_detail` (`valid`, `created_time`);

-- 库存流水复合索引
CREATE INDEX `idx_inventory_type_warehouse_time` ON `t_inventory_transaction` (`transaction_type`, `warehouse_id`, `transaction_time`);
CREATE INDEX `idx_inventory_category_time` ON `t_inventory_transaction` (`first_category`, `second_category`, `transaction_time`);
