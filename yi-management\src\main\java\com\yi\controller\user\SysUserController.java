package com.yi.controller.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yi.common.Result;
import com.yi.controller.user.model.*;
import com.yi.service.TUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 系统用户管理 前端控制器
 */
@RestController
@RequestMapping("/api/sys-user")
@Api(tags = "系统用户管理")
public class  SysUserController {

    @Autowired
    private TUserService tUserService;

    @ApiOperation("分页查询用户列表")
    @PostMapping("/page")
    public Result<IPage<UserPageResponse>> getUserPage(@RequestBody UserQueryRequest request) {
        IPage<UserPageResponse> page = tUserService.getUserPageResponse(request);
        return Result.success(page);
    }

    @ApiOperation("根据ID获取用户详情")
    @GetMapping("/{id}")
    public Result<UserDetailResponse> getUserById(@ApiParam("用户ID") @PathVariable Long id) {
        UserDetailResponse response = tUserService.getUserDetailById(id);
        if (response == null) {
            return Result.failed("用户不存在");
        }
        return Result.success(response);
    }

    @ApiOperation("新增用户")
    @PostMapping("/add")
    public Result<Boolean> addUser(@Valid @RequestBody UserRequest request) {
        try {
            boolean success = tUserService.addUser(request);
            if (success) {
                return Result.success("新增成功", true);
            }
            return Result.failed("新增失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("编辑用户")
    @PutMapping("/edit")
    public Result<Boolean> editUser(@Valid @RequestBody UserRequest request) {
        if (request.getId() == null) {
            return Result.failed("用户ID不能为空");
        }
        try {
            boolean success = tUserService.updateUser(request);
            if (success) {
                return Result.success("编辑成功", true);
            }
            return Result.failed("编辑失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("删除用户")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteUser(@ApiParam("用户ID") @PathVariable Long id) {
        boolean success = tUserService.deleteUser(id);
        if (success) {
            return Result.success("删除成功", true);
        }
        return Result.failed("删除失败");
    }

    @ApiOperation("批量删除用户")
    @DeleteMapping("/batch")
    public Result<Boolean> deleteUsers(@RequestBody List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Result.failed("用户ID列表不能为空");
        }
        boolean success = tUserService.deleteUsers(userIds);
        if (success) {
            return Result.success("批量删除成功", true);
        }
        return Result.failed("批量删除失败");
    }

    @ApiOperation("启用/禁用用户")
    @PutMapping("/{id}/status")
    public Result<Boolean> updateUserStatus(@ApiParam("用户ID") @PathVariable Long id,
                                            @ApiParam("状态") @RequestParam Integer status) {
        boolean success = tUserService.updateStatus(id, status);
        if (success) {
            String message = status == 1 ? "启用成功" : "禁用成功";
            return Result.success(message, true);
        }
        return Result.failed("状态更新失败");
    }

    @ApiOperation("重置用户密码")
    @PutMapping("/reset-password")
    public Result<Boolean> resetPassword(@Valid @RequestBody PasswordResetRequest request) {
        boolean success = tUserService.resetPassword(request.getUserId(), request.getNewPassword());
        if (success) {
            return Result.success("密码重置成功", true);
        }
        return Result.failed("密码重置失败");
    }

    @ApiOperation("修改用户密码")
    @PutMapping("/change-password")
    public Result<Boolean> changePassword(@Valid @RequestBody PasswordChangeRequest request) {
        try {
            boolean success = tUserService.changePassword(request.getUserId(),
                    request.getOldPassword(), request.getNewPassword());
            if (success) {
                return Result.success("密码修改成功", true);
            }
            return Result.failed("密码修改失败");
        } catch (RuntimeException e) {
            return Result.failed(e.getMessage());
        }
    }

    @ApiOperation("分配用户角色")
    @PutMapping("/{id}/roles")
    public Result<Boolean> assignRoles(@ApiParam("用户ID") @PathVariable Long id,
                                       @RequestBody List<Long> roleIds) {
        boolean success = tUserService.assignRoles(id, roleIds);
        if (success) {
            return Result.success("角色分配成功", true);
        }
        return Result.failed("角色分配失败");
    }

    @ApiOperation("导出用户列表")
    @PostMapping("/export")
    public void exportUserList(@RequestBody UserQueryRequest request,
                               HttpServletResponse response) throws IOException {
        tUserService.exportUserList(request, response);
    }

    @ApiOperation("检查用户名是否存在")
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username,
                                         @RequestParam(required = false) Long excludeId) {
        boolean exists = tUserService.checkUsernameExists(username, excludeId);
        return Result.success(exists);
    }

    @ApiOperation("检查邮箱是否存在")
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email,
                                      @RequestParam(required = false) Long excludeId) {
        boolean exists = tUserService.checkEmailExists(email, excludeId);
        return Result.success(exists);
    }

    @ApiOperation("检查手机号是否存在")
    @GetMapping("/check-phone")
    public Result<Boolean> checkPhone(@RequestParam String phone,
                                      @RequestParam(required = false) Long excludeId) {
        boolean exists = tUserService.checkPhoneExists(phone, excludeId);
        return Result.success(exists);
    }
}
