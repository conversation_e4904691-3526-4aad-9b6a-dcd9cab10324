Index: src/main/java/com/yi/utils/BcryptUtils.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.yi.utils;\r\n\r\nimport cn.hutool.crypto.digest.BCrypt;\r\n\r\npublic class BcryptUtils {\r\n\r\n\r\n    /**\r\n     * 给用户随机一个BCrypt的盐值\r\n     * @return\r\n     */\r\n    public  static String genSaltForUser(){\r\n        return BCrypt.gensalt();\r\n    }\r\n\r\n\r\n    /**\r\n     * 给用户随机一个BCrypt的盐值\r\n     * @return\r\n     */\r\n    public static String bcryptForPassword(String password,String salt){\r\n        return  BCrypt.hashpw(password,salt);\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/yi/utils/BcryptUtils.java b/src/main/java/com/yi/utils/BcryptUtils.java
--- a/src/main/java/com/yi/utils/BcryptUtils.java	(revision 750153a445f14029c68b34d0c55025850d47e19a)
+++ b/src/main/java/com/yi/utils/BcryptUtils.java	(date 1753974465406)
@@ -1,7 +1,5 @@
 package com.yi.utils;
 
-import cn.hutool.crypto.digest.BCrypt;
-
 public class BcryptUtils {
 
 
@@ -9,16 +7,16 @@
      * 给用户随机一个BCrypt的盐值
      * @return
      */
-    public  static String genSaltForUser(){
-        return BCrypt.gensalt();
-    }
+//    public  static String genSaltForUser(){
+//        return BCrypt.gensalt();
+//    }
 
 
     /**
      * 给用户随机一个BCrypt的盐值
      * @return
      */
-    public static String bcryptForPassword(String password,String salt){
-        return  BCrypt.hashpw(password,salt);
-    }
+//    public static String bcryptForPassword(String password,String salt){
+//        return  BCrypt.hashpw(password,salt);
+//    }
 }
Index: src/main/java/com/yi/configuration/web/WebConfig.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.yi.configuration.web;\r\n\r\n\r\nimport com.yi.configuration.interceptor.AppAuthRestInterceptor;\r\nimport org.springframework.context.annotation.Bean;\r\nimport org.springframework.context.annotation.Configuration;\r\nimport org.springframework.context.annotation.Primary;\r\nimport org.springframework.web.servlet.config.annotation.InterceptorRegistry;\r\nimport org.springframework.web.servlet.config.annotation.WebMvcConfigurer;\r\n\r\nimport java.util.ArrayList;\r\nimport java.util.Collections;\r\n\r\n@Primary\r\n@Configuration\r\npublic class WebConfig implements WebMvcConfigurer {\r\n    @Override\r\n    public void addInterceptors(InterceptorRegistry registry) {\r\n        ArrayList<String> commonPathPatterns = getExcludeCommonPathPatterns();\r\n        registry.addInterceptor(getAuthRestInterceptor())\r\n                .addPathPatterns(\"/**\")\r\n                .excludePathPatterns(commonPathPatterns.toArray(new String[]{}));\r\n    }\r\n\r\n    @Bean\r\n    AppAuthRestInterceptor getAuthRestInterceptor() {\r\n        return new AppAuthRestInterceptor();\r\n    }\r\n\r\n    private ArrayList<String> getExcludeCommonPathPatterns() {\r\n        ArrayList<String> list = new ArrayList<>();\r\n        String[] urls = {\r\n                \"/api/user/login\",\r\n                \"/cache/**\",\r\n                \"/webjars/**\",\r\n                \"/v2/api-docs\",\r\n                \"/swagger-ui.html\",\r\n                \"/swagger-resources/**\",\r\n                \"/error\"\r\n        };\r\n        Collections.addAll(list, urls);\r\n        return list;\r\n    }\r\n}\r\n\r\n\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/yi/configuration/web/WebConfig.java b/src/main/java/com/yi/configuration/web/WebConfig.java
--- a/src/main/java/com/yi/configuration/web/WebConfig.java	(revision 750153a445f14029c68b34d0c55025850d47e19a)
+++ b/src/main/java/com/yi/configuration/web/WebConfig.java	(date 1753974465412)
@@ -5,6 +5,7 @@
 import org.springframework.context.annotation.Bean;
 import org.springframework.context.annotation.Configuration;
 import org.springframework.context.annotation.Primary;
+import org.springframework.web.servlet.config.annotation.CorsRegistry;
 import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
 import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
 
@@ -22,6 +23,19 @@
                 .excludePathPatterns(commonPathPatterns.toArray(new String[]{}));
     }
 
+    /**
+     * 配置跨域请求
+     */
+    @Override
+    public void addCorsMappings(CorsRegistry registry) {
+        registry.addMapping("/**")
+                .allowedOrigins("http://localhost:5173", "http://127.0.0.1:5173")
+                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
+                .allowedHeaders("*")
+                .allowCredentials(true)
+                .maxAge(3600);
+    }
+
     @Bean
     AppAuthRestInterceptor getAuthRestInterceptor() {
         return new AppAuthRestInterceptor();
@@ -30,7 +44,9 @@
     private ArrayList<String> getExcludeCommonPathPatterns() {
         ArrayList<String> list = new ArrayList<>();
         String[] urls = {
-                "/api/user/login",
+                "/api/management/login",
+                "/api/management/logout",
+                "/api/customer-company/**",
                 "/cache/**",
                 "/webjars/**",
                 "/v2/api-docs",
Index: src/main/resources/sql/shipping-order-add-fields.sql
===================================================================
diff --git a/src/main/resources/sql/shipping-order-add-fields.sql b/src/main/resources/sql/shipping-order-add-fields.sql
deleted file mode 100644
--- a/src/main/resources/sql/shipping-order-add-fields.sql	(revision 750153a445f14029c68b34d0c55025850d47e19a)
+++ /dev/null	(revision 750153a445f14029c68b34d0c55025850d47e19a)
@@ -1,55 +0,0 @@
--- =============================================
--- 发运订单表添加收货人和地址字段
--- =============================================
-
--- 添加收货人信息字段
-ALTER TABLE `t_shipping_order` 
-ADD COLUMN `receiver_name` varchar(100) DEFAULT NULL COMMENT '收货人' AFTER `warehouse_id`;
-
-ALTER TABLE `t_shipping_order` 
-ADD COLUMN `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人手机号' AFTER `receiver_name`;
-
--- 添加地址信息字段
-ALTER TABLE `t_shipping_order` 
-ADD COLUMN `province` varchar(50) DEFAULT NULL COMMENT '省份' AFTER `receiver_phone`;
-
-ALTER TABLE `t_shipping_order` 
-ADD COLUMN `city` varchar(50) DEFAULT NULL COMMENT '城市' AFTER `province`;
-
-ALTER TABLE `t_shipping_order` 
-ADD COLUMN `district` varchar(50) DEFAULT NULL COMMENT '区县' AFTER `city`;
-
-ALTER TABLE `t_shipping_order` 
-ADD COLUMN `detail_address` varchar(500) DEFAULT NULL COMMENT '详细地址' AFTER `district`;
-
--- =============================================
--- 验证字段添加结果
--- =============================================
-
--- 查看表结构
-DESC `t_shipping_order`;
-
--- 检查新字段是否添加成功
-SELECT 
-    COLUMN_NAME,
-    DATA_TYPE,
-    IS_NULLABLE,
-    COLUMN_DEFAULT,
-    COLUMN_COMMENT
-FROM INFORMATION_SCHEMA.COLUMNS 
-WHERE TABLE_SCHEMA = DATABASE() 
-AND TABLE_NAME = 't_shipping_order' 
-AND COLUMN_NAME IN ('receiver_name', 'receiver_phone', 'province', 'city', 'district', 'detail_address')
-ORDER BY ORDINAL_POSITION;
-
--- =============================================
--- 回滚脚本（如需要）
--- =============================================
-
--- 删除添加的字段
--- ALTER TABLE `t_shipping_order` DROP COLUMN `receiver_name`;
--- ALTER TABLE `t_shipping_order` DROP COLUMN `receiver_phone`;
--- ALTER TABLE `t_shipping_order` DROP COLUMN `province`;
--- ALTER TABLE `t_shipping_order` DROP COLUMN `city`;
--- ALTER TABLE `t_shipping_order` DROP COLUMN `district`;
--- ALTER TABLE `t_shipping_order` DROP COLUMN `detail_address`;
Index: src/main/resources/sql/shipping-order-constraints.sql
===================================================================
diff --git a/src/main/resources/sql/shipping-order-constraints.sql b/src/main/resources/sql/shipping-order-constraints.sql
deleted file mode 100644
--- a/src/main/resources/sql/shipping-order-constraints.sql	(revision 750153a445f14029c68b34d0c55025850d47e19a)
+++ /dev/null	(revision 750153a445f14029c68b34d0c55025850d47e19a)
@@ -1,94 +0,0 @@
--- =============================================
--- 发运订单表约束和索引优化
--- =============================================
-
--- 1. 添加订单号唯一约束（防止并发重复）
-ALTER TABLE `t_shipping_order` 
-ADD UNIQUE KEY `uk_order_no` (`order_no`) COMMENT '订单号唯一约束';
-
--- 2. 添加复合索引优化查询性能
-ALTER TABLE `t_shipping_order` 
-ADD INDEX `idx_order_no_date` (`order_no`, `created_time`) COMMENT '订单号和创建时间复合索引';
-
--- 3. 添加状态和有效性复合索引
-ALTER TABLE `t_shipping_order` 
-ADD INDEX `idx_status_valid` (`status`, `valid`) COMMENT '状态和有效性复合索引';
-
--- 4. 添加客户和合同复合索引
-ALTER TABLE `t_shipping_order` 
-ADD INDEX `idx_customer_contract` (`customer_company_id`, `contract_code`) COMMENT '客户和合同复合索引';
-
--- 5. 添加创建时间索引（用于序号查询优化）
-ALTER TABLE `t_shipping_order` 
-ADD INDEX `idx_created_time` (`created_time`) COMMENT '创建时间索引';
-
--- =============================================
--- 可选：创建订单序号表（高并发场景使用）
--- =============================================
-
--- 订单序号表（用于原子性序号生成）
-CREATE TABLE `t_order_sequence` (
-    `date_str` varchar(8) NOT NULL COMMENT '日期字符串(yyyyMMdd)',
-    `current_seq` int(11) NOT NULL DEFAULT '0' COMMENT '当前序号',
-    `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
-    `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
-    PRIMARY KEY (`date_str`)
-) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单序号表';
-
--- =============================================
--- 数据完整性检查脚本
--- =============================================
-
--- 检查是否有重复的订单号
-SELECT order_no, COUNT(*) as count 
-FROM t_shipping_order 
-WHERE valid = 1 
-GROUP BY order_no 
-HAVING COUNT(*) > 1;
-
--- 检查订单号格式是否正确
-SELECT order_no 
-FROM t_shipping_order 
-WHERE valid = 1 
-AND order_no NOT REGEXP '^XSD[0-9]{8}[0-9]{4}$';
-
--- 检查序号是否连续（可选检查）
-SELECT 
-    DATE(created_time) as order_date,
-    COUNT(*) as total_orders,
-    MAX(CAST(RIGHT(order_no, 4) AS UNSIGNED)) as max_sequence
-FROM t_shipping_order 
-WHERE valid = 1 
-AND order_no REGEXP '^XSD[0-9]{8}[0-9]{4}$'
-GROUP BY DATE(created_time)
-ORDER BY order_date DESC;
-
--- =============================================
--- 性能优化建议
--- =============================================
-
--- 1. 定期清理无效数据（软删除的记录）
--- DELETE FROM t_shipping_order WHERE valid = 0 AND created_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);
-
--- 2. 定期分析表统计信息
--- ANALYZE TABLE t_shipping_order;
-
--- 3. 监控索引使用情况
--- SHOW INDEX FROM t_shipping_order;
-
--- 4. 检查慢查询
--- SELECT * FROM mysql.slow_log WHERE sql_text LIKE '%t_shipping_order%';
-
--- =============================================
--- 回滚脚本（如需要）
--- =============================================
-
--- 删除添加的约束和索引
--- ALTER TABLE `t_shipping_order` DROP INDEX `uk_order_no`;
--- ALTER TABLE `t_shipping_order` DROP INDEX `idx_order_no_date`;
--- ALTER TABLE `t_shipping_order` DROP INDEX `idx_status_valid`;
--- ALTER TABLE `t_shipping_order` DROP INDEX `idx_customer_contract`;
--- ALTER TABLE `t_shipping_order` DROP INDEX `idx_created_time`;
-
--- 删除序号表
--- DROP TABLE IF EXISTS `t_order_sequence`;
