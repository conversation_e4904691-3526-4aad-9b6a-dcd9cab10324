package com.yi.service;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.controller.user.model.*;

import com.yi.entity.TRole;
import com.yi.entity.TUser;
import com.yi.entity.TUserRole;
import com.yi.mapper.TUserMapper;
import com.yi.utils.FormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户表 服务类
 */
@Service
public class TUserService extends ServiceImpl<TUserMapper, TUser> {

    @Autowired
    private TUserRoleService tUserRoleService;

    @Autowired
    private TRoleService tRoleService;



    /**
     * 分页查询用户列表
     */
    public IPage<TUser> selectUserPage(Page<TUser> page, String username, String realName,
                                       String email, String phone, Integer status) {
        return baseMapper.selectUserPage(page, username, realName, email, phone, status);
    }

    /**
     * 根据用户名查询用户
     */
    public TUser selectByUsername(String username) {
        return baseMapper.selectByUsername(username);
    }

    /**
     * 根据邮箱查询用户
     */
    public TUser selectByEmail(String email) {
        return baseMapper.selectByEmail(email);
    }

    /**
     * 根据手机号查询用户
     */
    public TUser selectByPhone(String phone) {
        return baseMapper.selectByPhone(phone);
    }



    /**
     * 根据角色ID查询用户列表
     */
    public List<TUser> selectByRoleId(Long roleId) {
        return baseMapper.selectByRoleId(roleId);
    }

    /**
     * 根据用户ID查询角色ID列表
     */
    public List<Long> selectRoleIdsByUserId(Long userId) {
        return baseMapper.selectRoleIdsByUserId(userId);
    }

    /**
     * 根据用户ID查询权限列表
     */
    public List<String> selectPermissionsByUserId(Long userId) {
        return baseMapper.selectPermissionsByUserId(userId);
    }

    /**
     * 创建用户
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(TUser user) {
        // 检查用户名是否存在
        if (checkUsernameExists(user.getUsername(), null)) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否存在
        if (StringUtils.hasText(user.getEmail()) && checkEmailExists(user.getEmail(), null)) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 检查手机号是否存在
        if (StringUtils.hasText(user.getPhone()) && checkPhoneExists(user.getPhone(), null)) {
            throw new RuntimeException("手机号已存在");
        }
        
        // 设置默认值
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认启用
        }
        if (!StringUtils.hasText(user.getPassword())) {
            user.setPassword("123456"); // 默认密码
        }
        
        // 密码加密
        user.setPassword(DigestUtils.md5DigestAsHex(user.getPassword().getBytes()));
        user.setValid(1);
        user.setCreatedTime(LocalDateTime.now());
        user.setLastModifiedTime(LocalDateTime.now());
        
        return save(user);
    }

    /**
     * 更新用户
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(TUser user) {
        // 检查用户名是否存在（排除自己）
        if (StringUtils.hasText(user.getUsername()) && 
            checkUsernameExists(user.getUsername(), user.getId())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否存在（排除自己）
        if (StringUtils.hasText(user.getEmail()) && 
            checkEmailExists(user.getEmail(), user.getId())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 检查手机号是否存在（排除自己）
        if (StringUtils.hasText(user.getPhone()) && 
            checkPhoneExists(user.getPhone(), user.getId())) {
            throw new RuntimeException("手机号已存在");
        }
        
        // 如果修改了密码，需要加密
        if (StringUtils.hasText(user.getPassword())) {
            user.setPassword(DigestUtils.md5DigestAsHex(user.getPassword().getBytes()));
        }
        
        user.setLastModifiedTime(LocalDateTime.now());
        return updateById(user);
    }

    /**
     * 删除用户
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Long id) {
        TUser user = getById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 逻辑删除
        TUser updateUser = new TUser();
        updateUser.setId(id);
        updateUser.setValid(0);
        updateUser.setLastModifiedTime(LocalDateTime.now());
        
        // 同时删除用户角色关联
        tUserRoleService.deleteByUserId(id);
        
        return updateById(updateUser);
    }

    /**
     * 批量删除用户
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUsers(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        
        for (Long id : ids) {
            deleteUser(id);
        }
        return true;
    }

    /**
     * 重置密码
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(Long id, String newPassword) {
        TUser user = new TUser();
        user.setId(id);
        user.setPassword(DigestUtils.md5DigestAsHex(newPassword.getBytes()));
        user.setLastModifiedTime(LocalDateTime.now());
        return updateById(user);
    }

    /**
     * 修改密码
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(Long id, String oldPassword, String newPassword) {
        TUser user = getById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 验证旧密码
        String encryptedOldPassword = DigestUtils.md5DigestAsHex(oldPassword.getBytes());
        if (!encryptedOldPassword.equals(user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }
        
        // 更新新密码
        user.setPassword(DigestUtils.md5DigestAsHex(newPassword.getBytes()));
        user.setLastModifiedTime(LocalDateTime.now());
        return updateById(user);
    }

    /**
     * 启用/禁用用户
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Integer status) {
        TUser user = new TUser();
        user.setId(id);
        user.setStatus(status);
        user.setLastModifiedTime(LocalDateTime.now());
        return updateById(user);
    }

    /**
     * 分配角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRoles(Long userId, List<Long> roleIds) {
        // 先删除原有角色
        tUserRoleService.deleteByUserId(userId);
        
        // 分配新角色
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<TUserRole> userRoles = new ArrayList<>();
            for (Long roleId : roleIds) {
                TUserRole userRole = new TUserRole();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRole.setCreatedTime(LocalDateTime.now());
                userRoles.add(userRole);
            }
            tUserRoleService.saveBatch(userRoles);
        }
        
        return true;
    }

    /**
     * 检查用户名是否存在
     */
    public boolean checkUsernameExists(String username, Long excludeId) {
        LambdaQueryWrapper<TUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TUser::getUsername, username)
               .eq(TUser::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(TUser::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    /**
     * 检查邮箱是否存在
     */
    public boolean checkEmailExists(String email, Long excludeId) {
        LambdaQueryWrapper<TUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TUser::getEmail, email)
               .eq(TUser::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(TUser::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    /**
     * 检查手机号是否存在
     */
    public boolean checkPhoneExists(String phone, Long excludeId) {
        LambdaQueryWrapper<TUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TUser::getPhone, phone)
               .eq(TUser::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(TUser::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    // ==================== Controller专用方法 ====================

    /**
     * 分页查询用户列表（返回响应VO）
     */
    public IPage<UserPageResponse> getUserPageResponse(UserQueryRequest request) {
        Page<TUser> page = new Page<>(Long.parseLong(request.getCurrent()), Long.parseLong(request.getSize()));

        IPage<TUser> userPage = selectUserPage(page, request.getUsername(), request.getRealName(),
                                               request.getEmail(), request.getPhone(), request.getStatus());

        // 转换为响应对象
        IPage<UserPageResponse> responsePage = new Page<>(Long.parseLong(request.getCurrent()), Long.parseLong(request.getSize()));
        responsePage.setTotal(userPage.getTotal());
        responsePage.setPages(userPage.getPages());

        List<UserPageResponse> responseList = userPage.getRecords().stream()
                .map(this::convertToPageResponse)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);

        return responsePage;
    }

    /**
     * 根据ID获取用户详情（返回响应VO）
     */
    public UserDetailResponse getUserDetailById(Long userId) {
        TUser user = getById(userId);
        if (user == null || user.getValid() == 0) {
            return null;
        }

        UserDetailResponse response = new UserDetailResponse();
        BeanUtils.copyProperties(user, response);

        // 查询用户角色
        List<Long> roleIds = tUserRoleService.selectRoleIdsByUserId(userId);
        response.setRoleIds(roleIds);

        return response;
    }

    /**
     * 新增用户（使用请求VO）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addUser(UserRequest request) {
        TUser user = new TUser();
        BeanUtils.copyProperties(request, user);

        boolean success = createUser(user);

        // 分配角色
        if (success && !CollectionUtils.isEmpty(request.getRoleIds())) {
            assignRoles(user.getId(), request.getRoleIds());
        }

        return success;
    }

    /**
     * 更新用户（使用请求VO）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(UserRequest request) {
        TUser user = new TUser();
        BeanUtils.copyProperties(request, user);

        boolean success = updateUser(user);

        // 更新角色分配
        if (success && request.getRoleIds() != null) {
            assignRoles(user.getId(), request.getRoleIds());
        }

        return success;
    }

    /**
     * 导出用户列表
     */
    public void exportUserList(UserQueryRequest request, HttpServletResponse response) throws IOException {
        // 查询所有符合条件的用户
        Page<TUser> page = new Page<>(1, 10000); // 导出时不分页，设置一个大的页面大小
        IPage<TUser> userPage = selectUserPage(page, request.getUsername(), request.getRealName(),
                                               request.getEmail(), request.getPhone(), request.getStatus());

        // 转换为导出VO
        List<UserExportVO> exportList = userPage.getRecords().stream()
                .map(this::convertToExportVO)
                .collect(Collectors.toList());

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("用户列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")), "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 导出Excel
        EasyExcel.write(response.getOutputStream(), UserExportVO.class)
                .sheet("用户列表")
                .doWrite(exportList);
    }

    // ==================== 私有转换方法 ====================

    /**
     * 转换为分页响应VO
     */
    private UserPageResponse convertToPageResponse(TUser user) {
        UserPageResponse response = new UserPageResponse();
        BeanUtils.copyProperties(user, response);

        // 查询用户角色名称
        List<Long> roleIds = tUserRoleService.selectRoleIdsByUserId(user.getId());
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<TRole> roles = new ArrayList<>(tRoleService.listByIds(roleIds));
            List<String> roleNames = roles.stream()
                    .map(TRole::getRoleName)
                    .collect(Collectors.toList());
            // 注意：UserPageResponse可能没有setRoleNames方法，先注释掉
            // response.setRoleNames(roleNames);
        }

        return response;
    }

    /**
     * 转换为导出VO
     */
    private UserExportVO convertToExportVO(TUser user) {
        UserExportVO exportVO = new UserExportVO();
        BeanUtils.copyProperties(user, exportVO);

        // 转换状态
        exportVO.setStatus(user.getStatus() == 1 ? "启用" : "禁用");

        // 转换性别
        if (user.getGender() != null) {
            exportVO.setGender(user.getGender() == 1 ? "男" : "女");
        }

        // 查询角色名称
        List<Long> roleIds = tUserRoleService.selectRoleIdsByUserId(user.getId());
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<TRole> roles = new ArrayList<>(tRoleService.listByIds(roleIds));
            String roleNames = roles.stream()
                    .map(TRole::getRoleName)
                    .collect(Collectors.joining(", "));
            exportVO.setRoles(roleNames);
        }

        // 格式化时间
        if (user.getCreatedTime() != null) {
            exportVO.setCreatedTime(user.getCreatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (user.getLoginTime() != null) {
            exportVO.setLoginTime(user.getLoginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (user.getBirthday() != null) {
            exportVO.setBirthday(user.getBirthday().toString());
        }

        return exportVO;
    }
}
