package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.OutboundOrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 出库单明细表 Mapper 接口
 */
@Mapper
public interface OutboundOrderDetailMapper extends BaseMapper<OutboundOrderDetail> {

    /**
     * 分页查询出库单明细列表
     *
     * @param page 分页参数
     * @param outboundOrderId 出库单ID
     * @param sourceInboundOrderId 来源入库单ID
     * @param sourceInboundOrderNo 来源入库单号
     * @return 分页结果
     */
    IPage<OutboundOrderDetail> selectDetailPage(Page<OutboundOrderDetail> page,
                                                @Param("outboundOrderId") Long outboundOrderId,
                                                @Param("sourceInboundOrderId") Long sourceInboundOrderId,
                                                @Param("sourceInboundOrderNo") String sourceInboundOrderNo);

    /**
     * 根据出库单ID查询明细列表
     *
     * @param outboundOrderId 出库单ID
     * @return 明细列表
     */
    List<OutboundOrderDetail> selectByOutboundOrderId(@Param("outboundOrderId") Long outboundOrderId);

    /**
     * 根据来源入库单ID查询明细列表
     *
     * @param sourceInboundOrderId 来源入库单ID
     * @return 明细列表
     */
    List<OutboundOrderDetail> selectBySourceInboundOrderId(@Param("sourceInboundOrderId") Long sourceInboundOrderId);

    /**
     * 根据来源入库单号查询明细列表
     *
     * @param sourceInboundOrderNo 来源入库单号
     * @return 明细列表
     */
    List<OutboundOrderDetail> selectBySourceInboundOrderNo(@Param("sourceInboundOrderNo") String sourceInboundOrderNo);

    /**
     * 查询出库单明细汇总信息
     *
     * @param outboundOrderId 出库单ID
     * @return 汇总信息
     */
    Map<String, Object> selectDetailSummary(@Param("outboundOrderId") Long outboundOrderId);

    /**
     * 批量插入出库单明细
     *
     * @param details 明细列表
     * @return 插入行数
     */
    int batchInsert(@Param("details") List<OutboundOrderDetail> details);

    /**
     * 批量更新出库单明细的实际数量
     *
     * @param details 明细列表
     * @return 更新行数
     */
    int batchUpdateActualQuantity(@Param("details") List<OutboundOrderDetail> details);

    /**
     * 批量更新出库单明细的回退数量
     *
     * @param details 明细列表
     * @return 更新行数
     */
    int batchUpdateRollbackQuantity(@Param("details") List<OutboundOrderDetail> details);

    /**
     * 根据出库单ID删除明细（逻辑删除）
     *
     * @param outboundOrderId 出库单ID
     * @param lastModifiedBy 最后修改人
     * @return 删除行数
     */
    int deleteByOutboundOrderId(@Param("outboundOrderId") Long outboundOrderId,
                                @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 统计出库单明细数量
     *
     * @param outboundOrderId 出库单ID
     * @return 明细数量
     */
    Integer countByOutboundOrderId(@Param("outboundOrderId") Long outboundOrderId);

    /**
     * 查询出库单的总计划数量
     *
     * @param outboundOrderId 出库单ID
     * @return 总计划数量
     */
    Integer sumPlannedQuantityByOutboundOrderId(@Param("outboundOrderId") Long outboundOrderId);

    /**
     * 查询出库单的总实际数量
     *
     * @param outboundOrderId 出库单ID
     * @return 总实际数量
     */
    Integer sumActualQuantityByOutboundOrderId(@Param("outboundOrderId") Long outboundOrderId);

    /**
     * 查询出库单的总回退数量
     *
     * @param outboundOrderId 出库单ID
     * @return 总回退数量
     */
    Integer sumRollbackQuantityByOutboundOrderId(@Param("outboundOrderId") Long outboundOrderId);

    /**
     * 查询来源入库单的使用情况
     *
     * @param sourceInboundOrderId 来源入库单ID
     * @return 使用情况统计
     */
    Map<String, Object> selectSourceUsageStatistics(@Param("sourceInboundOrderId") Long sourceInboundOrderId);
}
