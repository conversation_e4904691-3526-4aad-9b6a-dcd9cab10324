package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 出库单明细表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_outbound_order_detail")
@ApiModel(value = "OutboundOrderDetail对象", description = "出库单明细表")
public class OutboundOrderDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 出库单ID
     */
    @ApiModelProperty(value = "出库单ID")
    @TableField("outbound_order_id")
    private Long outboundOrderId;

    /**
     * 计划出库数量
     */
    @ApiModelProperty(value = "计划出库数量")
    @TableField("planned_quantity")
    private Integer plannedQuantity;

    /**
     * 实际出库数量
     */
    @ApiModelProperty(value = "实际出库数量")
    @TableField("actual_quantity")
    private Integer actualQuantity;

    /**
     * 回退数量
     */
    @ApiModelProperty(value = "回退数量")
    @TableField("rollback_quantity")
    private Integer rollbackQuantity;

    /**
     * 来源入库单ID
     */
    @ApiModelProperty(value = "来源入库单ID")
    @TableField("source_inbound_order_id")
    private Long sourceInboundOrderId;

    /**
     * 来源入库单号
     */
    @ApiModelProperty(value = "来源入库单号")
    @TableField("source_inbound_order_no")
    private String sourceInboundOrderNo;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
}
