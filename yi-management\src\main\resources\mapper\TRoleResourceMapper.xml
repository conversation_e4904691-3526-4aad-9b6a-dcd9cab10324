<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TRoleResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TRoleResource">
        <id column="id" property="id" />
        <result column="role_id" property="roleId" />
        <result column="resource_id" property="resourceId" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_id, resource_id, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <!-- 根据角色ID删除角色资源关联 -->
    <delete id="deleteByRoleId">
        UPDATE t_role_resource 
        SET valid = 0, last_modified_time = NOW()
        WHERE role_id = #{roleId} AND valid = 1
    </delete>

    <!-- 根据资源ID删除角色资源关联 -->
    <delete id="deleteByResourceId">
        UPDATE t_role_resource 
        SET valid = 0, last_modified_time = NOW()
        WHERE resource_id = #{resourceId} AND valid = 1
    </delete>

    <!-- 批量插入角色资源关联 -->
    <insert id="batchInsert">
        INSERT INTO t_role_resource (role_id, resource_id, created_by, created_time, valid)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId}, #{item.resourceId}, #{item.createdBy}, #{item.createdTime}, 1)
        </foreach>
    </insert>

    <!-- 根据角色ID查询资源ID列表 -->
    <select id="selectResourceIdsByRoleId" resultType="java.lang.Long">
        SELECT resource_id
        FROM t_role_resource
        WHERE role_id = #{roleId} AND valid = 1
    </select>

    <!-- 根据资源ID查询角色ID列表 -->
    <select id="selectRoleIdsByResourceId" resultType="java.lang.Long">
        SELECT role_id
        FROM t_role_resource
        WHERE resource_id = #{resourceId} AND valid = 1
    </select>

    <!-- 根据角色ID查询资源ID列表 -->
    <select id="selectResourceIdsByRoleIdList" resultType="java.lang.Long">
        SELECT resource_id
        FROM sys_role_resource
        WHERE valid = 1 and role_id in
        <foreach collection="roleIdList" item="roleId" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
    </select>
</mapper>
