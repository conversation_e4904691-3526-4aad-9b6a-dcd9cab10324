package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.InboundOrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 入库单明细表 Mapper 接口
 */
@Mapper
public interface InboundOrderDetailMapper extends BaseMapper<InboundOrderDetail> {

    /**
     * 分页查询入库单明细列表
     *
     * @param page 分页参数
     * @param inboundOrderId 入库单ID
     * @param sourceInboundOrderId 来源入库单ID
     * @param sourceInboundOrderNo 来源入库单号
     * @return 分页结果
     */
    IPage<InboundOrderDetail> selectDetailPage(Page<InboundOrderDetail> page,
                                               @Param("inboundOrderId") Long inboundOrderId,
                                               @Param("sourceInboundOrderId") Long sourceInboundOrderId,
                                               @Param("sourceInboundOrderNo") String sourceInboundOrderNo);

    /**
     * 根据入库单ID查询明细列表
     *
     * @param inboundOrderId 入库单ID
     * @return 明细列表
     */
    List<InboundOrderDetail> selectByInboundOrderId(@Param("inboundOrderId") Long inboundOrderId);

    /**
     * 根据来源入库单ID查询明细列表
     *
     * @param sourceInboundOrderId 来源入库单ID
     * @return 明细列表
     */
    List<InboundOrderDetail> selectBySourceInboundOrderId(@Param("sourceInboundOrderId") Long sourceInboundOrderId);

    /**
     * 根据来源入库单号查询明细列表
     *
     * @param sourceInboundOrderNo 来源入库单号
     * @return 明细列表
     */
    List<InboundOrderDetail> selectBySourceInboundOrderNo(@Param("sourceInboundOrderNo") String sourceInboundOrderNo);

    /**
     * 查询入库单明细汇总信息
     *
     * @param inboundOrderId 入库单ID
     * @return 汇总信息
     */
    Map<String, Object> selectDetailSummary(@Param("inboundOrderId") Long inboundOrderId);

    /**
     * 批量插入入库单明细
     *
     * @param details 明细列表
     * @return 插入行数
     */
    int batchInsert(@Param("details") List<InboundOrderDetail> details);

    /**
     * 批量更新入库单明细的实际数量
     *
     * @param details 明细列表
     * @return 更新行数
     */
    int batchUpdateActualQuantity(@Param("details") List<InboundOrderDetail> details);

    /**
     * 批量更新入库单明细的回退数量
     *
     * @param details 明细列表
     * @return 更新行数
     */
    int batchUpdateRollbackQuantity(@Param("details") List<InboundOrderDetail> details);

    /**
     * 根据入库单ID删除明细（逻辑删除）
     *
     * @param inboundOrderId 入库单ID
     * @param lastModifiedBy 最后修改人
     * @return 删除行数
     */
    int deleteByInboundOrderId(@Param("inboundOrderId") Long inboundOrderId,
                               @Param("lastModifiedBy") String lastModifiedBy);

    /**
     * 统计入库单明细数量
     *
     * @param inboundOrderId 入库单ID
     * @return 明细数量
     */
    Integer countByInboundOrderId(@Param("inboundOrderId") Long inboundOrderId);

    /**
     * 查询入库单的总计划数量
     *
     * @param inboundOrderId 入库单ID
     * @return 总计划数量
     */
    Integer sumPlannedQuantityByInboundOrderId(@Param("inboundOrderId") Long inboundOrderId);

    /**
     * 查询入库单的总实际数量
     *
     * @param inboundOrderId 入库单ID
     * @return 总实际数量
     */
    Integer sumActualQuantityByInboundOrderId(@Param("inboundOrderId") Long inboundOrderId);

    /**
     * 查询入库单的总回退数量
     *
     * @param inboundOrderId 入库单ID
     * @return 总回退数量
     */
    Integer sumRollbackQuantityByInboundOrderId(@Param("inboundOrderId") Long inboundOrderId);

    /**
     * 查询来源入库单的使用情况
     *
     * @param sourceInboundOrderId 来源入库单ID
     * @return 使用情况统计
     */
    Map<String, Object> selectSourceUsageStatistics(@Param("sourceInboundOrderId") Long sourceInboundOrderId);

    /**
     * 查询可用于出库的入库单明细
     *
     * @param warehouseId 仓库ID
     * @param firstCategory 一级类目
     * @param secondCategory 二级类目
     * @return 可用明细列表
     */
    List<InboundOrderDetail> selectAvailableForOutbound(@Param("warehouseId") Long warehouseId,
                                                        @Param("firstCategory") Integer firstCategory,
                                                        @Param("secondCategory") String secondCategory);
}
