{"groups": [{"name": "file.upload", "type": "com.yi.config.FileUploadConfig", "sourceType": "com.yi.config.FileUploadConfig"}], "properties": [{"name": "file.upload.allowed-types", "type": "java.lang.String[]", "description": "允许上传的文件类型", "sourceType": "com.yi.config.FileUploadConfig", "defaultValue": ["jpg", "jpeg", "png", "gif", "bmp", "webp", "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "csv", "zip", "rar", "7z"]}, {"name": "file.upload.base-path", "type": "java.lang.String", "description": "正式文件存储根路径", "sourceType": "com.yi.config.FileUploadConfig", "defaultValue": "/uploads"}, {"name": "file.upload.max-file-size", "type": "java.lang.Long", "description": "单个文件最大大小（字节）默认10MB", "sourceType": "com.yi.config.FileUploadConfig", "defaultValue": 0}, {"name": "file.upload.max-request-size", "type": "java.lang.Long", "description": "总上传大小限制（字节）默认50MB", "sourceType": "com.yi.config.FileUploadConfig", "defaultValue": 0}, {"name": "file.upload.temp-file-retention-hours", "type": "java.lang.Integer", "description": "临时文件保留时间（小时）默认24小时", "sourceType": "com.yi.config.FileUploadConfig", "defaultValue": 24}, {"name": "file.upload.temp-path", "type": "java.lang.String", "description": "临时文件上传根路径", "sourceType": "com.yi.config.FileUploadConfig", "defaultValue": "/uploads/temp"}, {"name": "file.upload.temp-url-prefix", "type": "java.lang.String", "description": "临时文件访问URL前缀", "sourceType": "com.yi.config.FileUploadConfig", "defaultValue": "/temp-files"}, {"name": "file.upload.url-prefix", "type": "java.lang.String", "description": "正式文件访问URL前缀", "sourceType": "com.yi.config.FileUploadConfig", "defaultValue": "/files"}], "hints": []}