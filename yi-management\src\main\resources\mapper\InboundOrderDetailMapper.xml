<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.InboundOrderDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.InboundOrderDetail">
        <id column="id" property="id" />
        <result column="inbound_order_id" property="inboundOrderId" />
        <result column="planned_quantity" property="plannedQuantity" />
        <result column="actual_quantity" property="actualQuantity" />
        <result column="rollback_quantity" property="rollbackQuantity" />
        <result column="source_inbound_order_id" property="sourceInboundOrderId" />
        <result column="source_inbound_order_no" property="sourceInboundOrderNo" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, inbound_order_id, planned_quantity, actual_quantity, rollback_quantity,
        source_inbound_order_id, source_inbound_order_no, created_by, created_time,
        last_modified_by, last_modified_time, valid, remark
    </sql>

    <!-- 分页查询入库单明细列表 -->
    <select id="selectDetailPage" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order_detail
        WHERE valid = 1
        <if test="inboundOrderId != null">
            AND inbound_order_id = #{inboundOrderId}
        </if>
        <if test="sourceInboundOrderId != null">
            AND source_inbound_order_id = #{sourceInboundOrderId}
        </if>
        <if test="sourceInboundOrderNo != null and sourceInboundOrderNo != ''">
            AND source_inbound_order_no LIKE CONCAT('%', #{sourceInboundOrderNo}, '%')
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据入库单ID查询明细列表 -->
    <select id="selectByInboundOrderId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order_detail
        WHERE inbound_order_id = #{inboundOrderId} AND valid = 1
        ORDER BY created_time ASC
    </select>

    <!-- 根据来源入库单ID查询明细列表 -->
    <select id="selectBySourceInboundOrderId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order_detail
        WHERE source_inbound_order_id = #{sourceInboundOrderId} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 根据来源入库单号查询明细列表 -->
    <select id="selectBySourceInboundOrderNo" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inbound_order_detail
        WHERE source_inbound_order_no = #{sourceInboundOrderNo} AND valid = 1
        ORDER BY created_time DESC
    </select>

    <!-- 查询入库单明细汇总信息 -->
    <select id="selectDetailSummary" resultType="java.util.Map">
        SELECT 
            COUNT(*) as detail_count,
            SUM(planned_quantity) as total_planned_quantity,
            SUM(actual_quantity) as total_actual_quantity,
            SUM(rollback_quantity) as total_rollback_quantity,
            GROUP_CONCAT(DISTINCT source_inbound_order_no) as source_order_nos
        FROM t_inbound_order_detail
        WHERE inbound_order_id = #{inboundOrderId} AND valid = 1
    </select>

    <!-- 批量插入入库单明细 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_inbound_order_detail (
            inbound_order_id, planned_quantity, actual_quantity, rollback_quantity,
            source_inbound_order_id, source_inbound_order_no, created_by, created_time,
            last_modified_by, last_modified_time, valid, remark
        ) VALUES
        <foreach collection="details" item="detail" separator=",">
            (
                #{detail.inboundOrderId}, #{detail.plannedQuantity}, #{detail.actualQuantity}, #{detail.rollbackQuantity},
                #{detail.sourceInboundOrderId}, #{detail.sourceInboundOrderNo}, #{detail.createdBy}, #{detail.createdTime},
                #{detail.lastModifiedBy}, #{detail.lastModifiedTime}, #{detail.valid}, #{detail.remark}
            )
        </foreach>
    </insert>

    <!-- 批量更新入库单明细的实际数量 -->
    <update id="batchUpdateActualQuantity">
        <foreach collection="details" item="detail" separator=";">
            UPDATE t_inbound_order_detail 
            SET actual_quantity = #{detail.actualQuantity},
                last_modified_by = #{detail.lastModifiedBy},
                last_modified_time = NOW()
            WHERE id = #{detail.id} AND valid = 1
        </foreach>
    </update>

    <!-- 批量更新入库单明细的回退数量 -->
    <update id="batchUpdateRollbackQuantity">
        <foreach collection="details" item="detail" separator=";">
            UPDATE t_inbound_order_detail 
            SET rollback_quantity = #{detail.rollbackQuantity},
                last_modified_by = #{detail.lastModifiedBy},
                last_modified_time = NOW()
            WHERE id = #{detail.id} AND valid = 1
        </foreach>
    </update>

    <!-- 根据入库单ID删除明细（逻辑删除） -->
    <update id="deleteByInboundOrderId">
        UPDATE t_inbound_order_detail 
        SET valid = 0,
            last_modified_by = #{lastModifiedBy},
            last_modified_time = NOW()
        WHERE inbound_order_id = #{inboundOrderId} AND valid = 1
    </update>

    <!-- 统计入库单明细数量 -->
    <select id="countByInboundOrderId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_inbound_order_detail
        WHERE inbound_order_id = #{inboundOrderId} AND valid = 1
    </select>

    <!-- 查询入库单的总计划数量 -->
    <select id="sumPlannedQuantityByInboundOrderId" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(planned_quantity), 0)
        FROM t_inbound_order_detail
        WHERE inbound_order_id = #{inboundOrderId} AND valid = 1
    </select>

    <!-- 查询入库单的总实际数量 -->
    <select id="sumActualQuantityByInboundOrderId" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(actual_quantity), 0)
        FROM t_inbound_order_detail
        WHERE inbound_order_id = #{inboundOrderId} AND valid = 1
    </select>

    <!-- 查询入库单的总回退数量 -->
    <select id="sumRollbackQuantityByInboundOrderId" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(rollback_quantity), 0)
        FROM t_inbound_order_detail
        WHERE inbound_order_id = #{inboundOrderId} AND valid = 1
    </select>

    <!-- 查询来源入库单的使用情况 -->
    <select id="selectSourceUsageStatistics" resultType="java.util.Map">
        SELECT 
            source_inbound_order_id,
            source_inbound_order_no,
            COUNT(*) as usage_count,
            SUM(planned_quantity) as total_planned_quantity,
            SUM(actual_quantity) as total_actual_quantity,
            SUM(rollback_quantity) as total_rollback_quantity
        FROM t_inbound_order_detail
        WHERE source_inbound_order_id = #{sourceInboundOrderId} AND valid = 1
        GROUP BY source_inbound_order_id, source_inbound_order_no
    </select>

    <!-- 查询可用于出库的入库单明细 -->
    <select id="selectAvailableForOutbound" resultMap="BaseResultMap">
        SELECT 
            d.id, d.inbound_order_id, d.planned_quantity, d.actual_quantity, d.rollback_quantity,
            d.source_inbound_order_id, d.source_inbound_order_no, d.created_by, d.created_time,
            d.last_modified_by, d.last_modified_time, d.valid, d.remark
        FROM t_inbound_order_detail d
        INNER JOIN t_inbound_order i ON d.inbound_order_id = i.id
        WHERE d.valid = 1 AND i.valid = 1
        AND i.status = 3  -- 已入库状态
        AND (d.actual_quantity - d.rollback_quantity) > 0  -- 有可用库存
        <if test="warehouseId != null">
            AND i.inbound_warehouse_id = #{warehouseId}
        </if>
        <if test="firstCategory != null">
            AND i.first_category = #{firstCategory}
        </if>
        <if test="secondCategory != null and secondCategory != ''">
            AND i.second_category = #{secondCategory}
        </if>
        ORDER BY i.inbound_time ASC  -- 先进先出
    </select>

</mapper>
