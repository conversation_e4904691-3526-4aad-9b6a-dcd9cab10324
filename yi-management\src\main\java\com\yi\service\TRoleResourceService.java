package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.TRoleResource;
import com.yi.mapper.TRoleResourceMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色资源关联表 服务类
 */
@Service
public class TRoleResourceService extends ServiceImpl<TRoleResourceMapper, TRoleResource> {

    /**
     * 根据角色ID删除角色资源关联
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByRoleId(Long roleId) {
        return baseMapper.deleteByRoleId(roleId);
    }

    /**
     * 根据资源ID删除角色资源关联
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByResourceId(Long resourceId) {
        return baseMapper.deleteByResourceId(resourceId);
    }

    /**
     * 批量插入角色资源关联
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<TRoleResource> roleResources) {
        if (CollectionUtils.isEmpty(roleResources)) {
            return 0;
        }
        
        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        roleResources.forEach(roleResource -> {
            roleResource.setCreatedTime(now);
        });
        
        return baseMapper.batchInsert(roleResources);
    }

    /**
     * 根据角色ID查询资源ID列表
     */
    public List<Long> selectResourceIdsByRoleId(Long roleId) {
        return baseMapper.selectResourceIdsByRoleId(roleId);
    }

    /**
     * 根据资源ID查询角色ID列表
     */
    public List<Long> selectRoleIdsByResourceId(Long resourceId) {
        return baseMapper.selectRoleIdsByResourceId(resourceId);
    }

    /**
     * 检查角色是否拥有指定资源
     */
    public boolean existsByRoleIdAndResourceId(Long roleId, Long resourceId) {
        LambdaQueryWrapper<TRoleResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TRoleResource::getRoleId, roleId)
               .eq(TRoleResource::getResourceId, resourceId);
        return count(wrapper) > 0;
    }
}
