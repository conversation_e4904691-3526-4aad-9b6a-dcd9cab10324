package com.yi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.InboundOrder;
import com.yi.entity.InboundOrderDetail;
import com.yi.mapper.InboundOrderDetailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 入库单明细表 服务类
 */
@Service
public class InboundOrderDetailService extends ServiceImpl<InboundOrderDetailMapper, InboundOrderDetail> {

    @Autowired
    private InboundOrderService inboundOrderService;

    /**
     * 分页查询入库单明细列表
     */
    public IPage<InboundOrderDetail> selectDetailPage(Page<InboundOrderDetail> page, Long inboundOrderId,
                                                      Long sourceInboundOrderId, String sourceInboundOrderNo) {
        return baseMapper.selectDetailPage(page, inboundOrderId, sourceInboundOrderId, sourceInboundOrderNo);
    }

    /**
     * 根据入库单ID查询明细列表
     */
    public List<InboundOrderDetail> selectByInboundOrderId(Long inboundOrderId) {
        return baseMapper.selectByInboundOrderId(inboundOrderId);
    }

    /**
     * 根据来源入库单ID查询明细列表
     */
    public List<InboundOrderDetail> selectBySourceInboundOrderId(Long sourceInboundOrderId) {
        return baseMapper.selectBySourceInboundOrderId(sourceInboundOrderId);
    }

    /**
     * 根据来源入库单号查询明细列表
     */
    public List<InboundOrderDetail> selectBySourceInboundOrderNo(String sourceInboundOrderNo) {
        return baseMapper.selectBySourceInboundOrderNo(sourceInboundOrderNo);
    }

    /**
     * 查询入库单明细汇总信息
     */
    public Map<String, Object> getDetailSummary(Long inboundOrderId) {
        return baseMapper.selectDetailSummary(inboundOrderId);
    }

    /**
     * 创建入库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createDetail(InboundOrderDetail detail) {
        // 设置默认值
        if (detail.getActualQuantity() == null) {
            detail.setActualQuantity(0);
        }
        if (detail.getRollbackQuantity() == null) {
            detail.setRollbackQuantity(0);
        }
        detail.setValid(1);
        detail.setCreatedTime(LocalDateTime.now());
        detail.setLastModifiedTime(LocalDateTime.now());

        return save(detail);
    }

    /**
     * 批量创建入库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateDetails(List<InboundOrderDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return true;
        }

        // 设置默认值
        LocalDateTime now = LocalDateTime.now();
        for (InboundOrderDetail detail : details) {
            if (detail.getActualQuantity() == null) {
                detail.setActualQuantity(0);
            }
            if (detail.getRollbackQuantity() == null) {
                detail.setRollbackQuantity(0);
            }
            detail.setValid(1);
            detail.setCreatedTime(now);
            detail.setLastModifiedTime(now);
        }

        return baseMapper.batchInsert(details) > 0;
    }

    /**
     * 更新入库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDetail(InboundOrderDetail detail) {
        detail.setLastModifiedTime(LocalDateTime.now());
        return updateById(detail);
    }

    /**
     * 批量更新入库单明细的实际数量
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateActualQuantity(List<InboundOrderDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return true;
        }

        return baseMapper.batchUpdateActualQuantity(details) > 0;
    }

    /**
     * 批量更新入库单明细的回退数量
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateRollbackQuantity(List<InboundOrderDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return true;
        }

        return baseMapper.batchUpdateRollbackQuantity(details) > 0;
    }

    /**
     * 删除入库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDetail(Long id) {
        InboundOrderDetail detail = new InboundOrderDetail();
        detail.setId(id);
        detail.setValid(0);
        detail.setLastModifiedTime(LocalDateTime.now());
        return updateById(detail);
    }

    /**
     * 根据入库单ID删除所有明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByInboundOrderId(Long inboundOrderId, String lastModifiedBy) {
        return baseMapper.deleteByInboundOrderId(inboundOrderId, lastModifiedBy) > 0;
    }

    /**
     * 统计入库单明细数量
     */
    public Integer countByInboundOrderId(Long inboundOrderId) {
        return baseMapper.countByInboundOrderId(inboundOrderId);
    }

    /**
     * 查询入库单的总计划数量
     */
    public Integer sumPlannedQuantityByInboundOrderId(Long inboundOrderId) {
        return baseMapper.sumPlannedQuantityByInboundOrderId(inboundOrderId);
    }

    /**
     * 查询入库单的总实际数量
     */
    public Integer sumActualQuantityByInboundOrderId(Long inboundOrderId) {
        return baseMapper.sumActualQuantityByInboundOrderId(inboundOrderId);
    }

    /**
     * 查询入库单的总回退数量
     */
    public Integer sumRollbackQuantityByInboundOrderId(Long inboundOrderId) {
        return baseMapper.sumRollbackQuantityByInboundOrderId(inboundOrderId);
    }

    /**
     * 查询来源入库单的使用情况
     */
    public Map<String, Object> getSourceUsageStatistics(Long sourceInboundOrderId) {
        return baseMapper.selectSourceUsageStatistics(sourceInboundOrderId);
    }

    /**
     * 查询可用于出库的入库单明细
     */
    public List<InboundOrderDetail> getAvailableForOutbound(Long warehouseId, Integer firstCategory, String secondCategory) {
        return baseMapper.selectAvailableForOutbound(warehouseId, firstCategory, secondCategory);
    }

    /**
     * 根据出库单自动生成入库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean generateDetailsFromSourceOrders(Long inboundOrderId, List<Long> sourceInboundOrderIds,
                                                   List<Integer> quantities, String createdBy) {
        if (CollectionUtils.isEmpty(sourceInboundOrderIds) || CollectionUtils.isEmpty(quantities)) {
            throw new RuntimeException("来源入库单ID和数量不能为空");
        }

        if (sourceInboundOrderIds.size() != quantities.size()) {
            throw new RuntimeException("来源入库单ID和数量的数量必须一致");
        }

        // 生成明细列表
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < sourceInboundOrderIds.size(); i++) {
            Long sourceInboundOrderId = sourceInboundOrderIds.get(i);
            Integer quantity = quantities.get(i);

            // 获取来源入库单信息
            InboundOrder sourceInboundOrder = inboundOrderService.getById(sourceInboundOrderId);
            if (sourceInboundOrder == null) {
                throw new RuntimeException("来源入库单不存在：" + sourceInboundOrderId);
            }

            // 创建入库单明细
            InboundOrderDetail detail = new InboundOrderDetail();
            detail.setInboundOrderId(inboundOrderId);
            detail.setPlannedQuantity(quantity);
            detail.setActualQuantity(0);
            detail.setRollbackQuantity(0);
            detail.setSourceInboundOrderId(sourceInboundOrderId);
            detail.setSourceInboundOrderNo(sourceInboundOrder.getOrderNo());
            detail.setCreatedBy(createdBy);
            detail.setCreatedTime(now);
            detail.setLastModifiedBy(createdBy);
            detail.setLastModifiedTime(now);
            detail.setValid(1);

            if (!save(detail)) {
                throw new RuntimeException("创建入库单明细失败");
            }
        }

        return true;
    }

    /**
     * 计算入库单的可用库存
     */
    public Integer calculateAvailableStock(Long inboundOrderId) {
        InboundOrder inboundOrder = inboundOrderService.getById(inboundOrderId);
        if (inboundOrder == null) {
            return 0;
        }

        // 可用库存 = 实际入库数量 - 回退数量
        Integer actualQuantity = inboundOrder.getActualQuantity() != null ? inboundOrder.getActualQuantity() : 0;
        Integer rollbackQuantity = sumRollbackQuantityByInboundOrderId(inboundOrderId);
        rollbackQuantity = rollbackQuantity != null ? rollbackQuantity : 0;

        return Math.max(0, actualQuantity - rollbackQuantity);
    }

    /**
     * 更新入库单明细的可用库存（扣减出库数量）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAvailableStock(Long inboundOrderId, Integer outboundQuantity, String lastModifiedBy) {
        if (outboundQuantity == null || outboundQuantity <= 0) {
            return true;
        }

        // 获取当前可用库存
        Integer availableStock = calculateAvailableStock(inboundOrderId);
        if (availableStock < outboundQuantity) {
            throw new RuntimeException("可用库存不足，当前可用：" + availableStock + "，需要：" + outboundQuantity);
        }

        // 更新入库单的实际数量（减少可用库存）
        InboundOrder inboundOrder = inboundOrderService.getById(inboundOrderId);
        if (inboundOrder != null) {
            inboundOrder.setActualQuantity(inboundOrder.getActualQuantity() - outboundQuantity);
            inboundOrder.setLastModifiedBy(lastModifiedBy);
            inboundOrder.setLastModifiedTime(LocalDateTime.now());
            return inboundOrderService.updateById(inboundOrder);
        }

        return false;
    }
}
