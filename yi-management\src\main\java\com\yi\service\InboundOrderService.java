package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.InboundOrder;
import com.yi.enums.InboundStatusEnum;
import com.yi.mapper.InboundOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 入库单表 服务类
 */
@Service
public class InboundOrderService extends ServiceImpl<InboundOrderMapper, InboundOrder> {

    @Autowired
    private OrderNumberService orderNumberService;

    /**
     * 分页查询入库单列表
     */
    public IPage<InboundOrder> selectInboundOrderPage(Page<InboundOrder> page, String orderNo, Integer status,
                                                      Integer inboundType, Long inboundWarehouseId, Long senderWarehouseId,
                                                      Integer firstCategory, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectInboundOrderPage(page, orderNo, status, inboundType, inboundWarehouseId,
                senderWarehouseId, firstCategory, startTime, endTime);
    }

    /**
     * 根据入库单号查询入库单
     */
    public InboundOrder selectByOrderNo(String orderNo) {
        return baseMapper.selectByOrderNo(orderNo);
    }

    /**
     * 根据状态查询入库单列表
     */
    public List<InboundOrder> selectByStatus(Integer status) {
        return baseMapper.selectByStatus(status);
    }

    /**
     * 根据关联出库单ID查询入库单
     */
    public InboundOrder selectByOutboundOrderId(Long outboundOrderId) {
        return baseMapper.selectByOutboundOrderId(outboundOrderId);
    }

    /**
     * 创建入库单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createInboundOrder(InboundOrder inboundOrder) {
        // 生成入库单号（如果没有提供）
        if (!StringUtils.hasText(inboundOrder.getOrderNo())) {
            String orderNo = orderNumberService.generateInboundOrderNo();
            inboundOrder.setOrderNo(orderNo);
        }

        // 检查单号是否已存在
        if (checkOrderNoExists(inboundOrder.getOrderNo(), null)) {
            throw new RuntimeException("入库单号已存在");
        }

        // 设置默认值
        if (inboundOrder.getStatus() == null) {
            inboundOrder.setStatus(InboundStatusEnum.PENDING.getCode());
        }
        if (inboundOrder.getActualQuantity() == null) {
            inboundOrder.setActualQuantity(0);
        }
        inboundOrder.setValid(1);
        inboundOrder.setCreatedTime(LocalDateTime.now());
        inboundOrder.setLastModifiedTime(LocalDateTime.now());

        return save(inboundOrder);
    }

    /**
     * 更新入库单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInboundOrder(InboundOrder inboundOrder) {
        // 检查单号是否已存在（排除自己）
        if (StringUtils.hasText(inboundOrder.getOrderNo()) && 
            checkOrderNoExists(inboundOrder.getOrderNo(), inboundOrder.getId())) {
            throw new RuntimeException("入库单号已存在");
        }

        inboundOrder.setLastModifiedTime(LocalDateTime.now());
        return updateById(inboundOrder);
    }

    /**
     * 删除入库单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteInboundOrder(Long id) {
        InboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 只有待入库状态的订单才能删除
        if (!InboundStatusEnum.PENDING.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有待入库状态的订单才能删除");
        }

        // 逻辑删除
        InboundOrder updateOrder = new InboundOrder();
        updateOrder.setId(id);
        updateOrder.setValid(0);
        updateOrder.setLastModifiedTime(LocalDateTime.now());
        return updateById(updateOrder);
    }

    /**
     * 批量删除入库单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteInboundOrders(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        for (Long id : ids) {
            deleteInboundOrder(id);
        }
        return true;
    }

    /**
     * 部分入库（更新状态为部分入库）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean partialInbound(Long id, Integer actualQuantity, String lastModifiedBy) {
        InboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 只有待入库状态的订单才能部分入库
        if (!InboundStatusEnum.PENDING.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有待入库状态的订单才能部分入库");
        }

        // 检查实际入库数不能超过计划入库数
        if (actualQuantity > order.getPlannedQuantity()) {
            throw new RuntimeException("实际入库数不能超过计划入库数");
        }

        // 更新状态为部分入库
        return baseMapper.updateStatus(id, InboundStatusEnum.PARTIAL.getCode(), 
                actualQuantity, LocalDateTime.now(), lastModifiedBy) > 0;
    }

    /**
     * 完成入库（更新状态为已入库）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean completeInbound(Long id, Integer actualQuantity, String lastModifiedBy) {
        InboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 待入库或部分入库状态的订单才能完成入库
        if (!InboundStatusEnum.PENDING.getCode().equals(order.getStatus()) && 
            !InboundStatusEnum.PARTIAL.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有待入库或部分入库状态的订单才能完成入库");
        }

        // 检查实际入库数不能超过计划入库数
        if (actualQuantity > order.getPlannedQuantity()) {
            throw new RuntimeException("实际入库数不能超过计划入库数");
        }

        // 更新状态为已入库
        return baseMapper.updateStatus(id, InboundStatusEnum.COMPLETED.getCode(), 
                actualQuantity, LocalDateTime.now(), lastModifiedBy) > 0;
    }

    /**
     * 取消入库（回退到待入库状态）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelInbound(Long id, String lastModifiedBy) {
        InboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("入库单不存在");
        }

        // 只有部分入库状态的订单才能取消
        if (!InboundStatusEnum.PARTIAL.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有部分入库状态的订单才能取消");
        }

        // 回退到待入库状态，清空实际入库数和入库时间
        return baseMapper.updateStatus(id, InboundStatusEnum.PENDING.getCode(), 
                0, null, lastModifiedBy) > 0;
    }

    /**
     * 统计各状态的入库单数量
     */
    public List<Map<String, Object>> getStatusStatistics() {
        return baseMapper.selectStatusStatistics();
    }

    /**
     * 统计各类型的入库单数量
     */
    public List<Map<String, Object>> getTypeStatistics() {
        return baseMapper.selectTypeStatistics();
    }

    /**
     * 查询待入库的订单
     */
    public List<InboundOrder> getPendingOrders() {
        return baseMapper.selectPendingOrders();
    }

    /**
     * 查询部分入库的订单
     */
    public List<InboundOrder> getPartialOrders() {
        return baseMapper.selectPartialOrders();
    }

    /**
     * 检查入库单号是否已存在
     */
    public boolean checkOrderNoExists(String orderNo, Long excludeId) {
        LambdaQueryWrapper<InboundOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InboundOrder::getOrderNo, orderNo)
               .eq(InboundOrder::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(InboundOrder::getId, excludeId);
        }
        return count(wrapper) > 0;
    }
}
