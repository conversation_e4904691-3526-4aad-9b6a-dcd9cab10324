-- =============================================
-- 出入库管理系统初始化数据
-- =============================================

-- 1. 初始化单号序列数据（当前日期）
INSERT INTO `t_order_sequence` (`date_key`, `sequence_value`, `created_time`, `last_modified_time`) VALUES
(DATE_FORMAT(NOW(), '%Y%m%d'), 0, NOW(), NOW());

-- 2. 初始化测试出库单数据
INSERT INTO `t_outbound_order` (
    `order_no`, `status`, `outbound_type`, `outbound_company_id`, `outbound_company_name`, 
    `outbound_address`, `delivery_method`, `vehicle_number`, `driver_name`, `driver_phone`,
    `receive_company_id`, `receive_company_name`, `receive_address`, 
    `first_category`, `second_category`, `planned_quantity`, `actual_quantity`, 
    `outbound_time`, `created_by`, `created_time`, `last_modified_by`, `last_modified_time`, `valid`, `remark`
) VALUES
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0001'), 1, 1, 1, '易托盘科技有限公司',
    '上海市浦东新区张江高科技园区', '物流配送', '沪A12345', '张师傅', '13800138001',
    2, '客户公司A', '北京市朝阳区建国门外大街1号', 
    1, '标准托盘', 100, 0, 
    NULL, 'system', NOW(), 'system', NOW(), 1, '测试出库单1'
),
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0002'), 2, 2, 1, '易托盘科技有限公司',
    '上海市浦东新区张江高科技园区', '自提', '沪B67890', '李师傅', '13800138002',
    3, '客户公司B', '广州市天河区珠江新城', 
    1, '加强型托盘', 200, 180, 
    NOW(), 'system', NOW(), 'system', NOW(), 1, '测试出库单2'
),
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0003'), 3, 1, 1, '易托盘科技有限公司',
    '上海市浦东新区张江高科技园区', '快递配送', '沪C11111', '王师傅', '13800138003',
    4, '客户公司C', '深圳市南山区科技园', 
    1, '轻型托盘', 50, 50, 
    DATE_SUB(NOW(), INTERVAL 1 DAY), 'system', NOW(), 'system', NOW(), 1, '测试出库单3'
);

-- 3. 初始化测试入库单数据
INSERT INTO `t_inbound_order` (
    `order_no`, `status`, `inbound_type`, `inbound_warehouse_id`, `inbound_warehouse_name`,
    `delivery_method`, `vehicle_number`, `driver_name`, `driver_phone`,
    `sender_warehouse_id`, `sender_warehouse_name`, `sender_address`,
    `first_category`, `second_category`, `planned_quantity`, `actual_quantity`,
    `inbound_time`, `outbound_order_id`, `created_by`, `created_time`, `last_modified_by`, `last_modified_time`, `valid`, `remark`
) VALUES
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0001'), 1, 4, 2, '北京仓库',
    '物流配送', '沪A12345', '张师傅', '13800138001',
    1, '上海总仓', '上海市浦东新区张江高科技园区',
    1, '标准托盘', 100, 0,
    NULL, 1, 'system', NOW(), 'system', NOW(), 1, '测试入库单1'
),
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0002'), 2, 3, 3, '广州仓库',
    '自提', '沪B67890', '李师傅', '13800138002',
    1, '上海总仓', '上海市浦东新区张江高科技园区',
    1, '加强型托盘', 200, 150,
    NOW(), 2, 'system', NOW(), 'system', NOW(), 1, '测试入库单2'
),
(
    CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0003'), 3, 4, 4, '深圳仓库',
    '快递配送', '沪C11111', '王师傅', '13800138003',
    1, '上海总仓', '上海市浦东新区张江高科技园区',
    1, '轻型托盘', 50, 50,
    DATE_SUB(NOW(), INTERVAL 1 DAY), 3, 'system', NOW(), 'system', NOW(), 1, '测试入库单3'
);

-- 4. 更新单号序列（模拟已生成3个单号）
UPDATE `t_order_sequence` SET `sequence_value` = 3, `last_modified_time` = NOW()
WHERE `date_key` = DATE_FORMAT(NOW(), '%Y%m%d');

-- =============================================
-- 出入库明细管理系统初始化数据
-- =============================================

-- 5. 初始化出库单明细数据
INSERT INTO `t_outbound_order_detail` (
    `outbound_order_id`, `planned_quantity`, `actual_quantity`, `rollback_quantity`,
    `source_inbound_order_id`, `source_inbound_order_no`,
    `created_by`, `created_time`, `last_modified_by`, `last_modified_time`, `valid`, `remark`
) VALUES
-- 出库单1的明细（使用入库单1作为来源）
(1, 50, 45, 0, 1, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0001'), 'system', NOW(), 'system', NOW(), 1, '出库明细1-1'),
(1, 50, 50, 0, 2, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0002'), 'system', NOW(), 'system', NOW(), 1, '出库明细1-2'),

-- 出库单2的明细（使用入库单2作为来源）
(2, 100, 90, 10, 2, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0002'), 'system', NOW(), 'system', NOW(), 1, '出库明细2-1'),
(2, 100, 90, 0, 3, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0003'), 'system', NOW(), 'system', NOW(), 1, '出库明细2-2'),

-- 出库单3的明细（使用入库单3作为来源）
(3, 50, 50, 0, 3, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0003'), 'system', NOW(), 'system', NOW(), 1, '出库明细3-1');

-- 6. 初始化入库单明细数据
INSERT INTO `t_inbound_order_detail` (
    `inbound_order_id`, `planned_quantity`, `actual_quantity`, `rollback_quantity`,
    `source_inbound_order_id`, `source_inbound_order_no`,
    `created_by`, `created_time`, `last_modified_by`, `last_modified_time`, `valid`, `remark`
) VALUES
-- 入库单1的明细（采购入库，无来源入库单）
(1, 100, 0, 0, NULL, NULL, 'system', NOW(), 'system', NOW(), 1, '采购入库明细1'),

-- 入库单2的明细（调拨入库，来源于其他仓库的入库单）
(2, 150, 150, 0, 1, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0001'), 'system', NOW(), 'system', NOW(), 1, '调拨入库明细2-1'),
(2, 50, 0, 0, NULL, NULL, 'system', NOW(), 'system', NOW(), 1, '调拨入库明细2-2'),

-- 入库单3的明细（销售退货入库）
(3, 50, 50, 0, NULL, NULL, 'system', NOW(), 'system', NOW(), 1, '销售退货入库明细3');

-- 7. 初始化库存流水数据
INSERT INTO `t_inventory_transaction` (
    `transaction_type`, `order_id`, `order_no`, `detail_id`, `warehouse_id`, `warehouse_name`,
    `first_category`, `second_category`, `quantity_before`, `quantity_change`, `quantity_after`,
    `transaction_time`, `created_by`, `remark`
) VALUES
-- 入库流水
(1, 1, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0001'), 1, 1, '上海总仓', 1, '标准托盘', 0, 100, 100, NOW(), 'system', '采购入库'),
(1, 2, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0002'), 2, 2, '北京仓库', 1, '加强型托盘', 0, 150, 150, NOW(), 'system', '调拨入库'),
(1, 3, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0003'), 4, 4, '深圳仓库', 1, '轻型托盘', 0, 50, 50, NOW(), 'system', '销售退货入库'),

-- 出库流水
(2, 1, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0001'), 1, 1, '上海总仓', 1, '标准托盘', 100, -45, 55, DATE_ADD(NOW(), INTERVAL 1 HOUR), 'system', '销售出库'),
(2, 1, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0001'), 2, 2, '北京仓库', 1, '加强型托盘', 150, -50, 100, DATE_ADD(NOW(), INTERVAL 1 HOUR), 'system', '销售出库'),
(2, 2, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0002'), 3, 2, '北京仓库', 1, '加强型托盘', 100, -90, 10, DATE_ADD(NOW(), INTERVAL 2 HOUR), 'system', '调拨出库'),
(2, 3, CONCAT('F', DATE_FORMAT(NOW(), '%Y%m%d'), '0003'), 5, 4, '深圳仓库', 1, '轻型托盘', 50, -50, 0, DATE_ADD(NOW(), INTERVAL 3 HOUR), 'system', '销售出库');

-- 8. 创建视图用于快速查询库存汇总
CREATE OR REPLACE VIEW `v_inventory_summary` AS
SELECT
    warehouse_id,
    warehouse_name,
    first_category,
    second_category,
    SUM(CASE WHEN transaction_type = 1 THEN quantity_change ELSE 0 END) as total_inbound,
    SUM(CASE WHEN transaction_type = 2 THEN ABS(quantity_change) ELSE 0 END) as total_outbound,
    SUM(quantity_change) as current_stock,
    COUNT(*) as transaction_count,
    MAX(transaction_time) as last_transaction_time
FROM t_inventory_transaction
GROUP BY warehouse_id, warehouse_name, first_category, second_category;

-- 9. 创建视图用于查询出库单明细汇总
CREATE OR REPLACE VIEW `v_outbound_detail_summary` AS
SELECT
    od.outbound_order_id,
    o.order_no as outbound_order_no,
    o.status as outbound_status,
    COUNT(od.id) as detail_count,
    SUM(od.planned_quantity) as total_planned_quantity,
    SUM(od.actual_quantity) as total_actual_quantity,
    SUM(od.rollback_quantity) as total_rollback_quantity,
    GROUP_CONCAT(DISTINCT od.source_inbound_order_no) as source_order_nos
FROM t_outbound_order_detail od
LEFT JOIN t_outbound_order o ON od.outbound_order_id = o.id
WHERE od.valid = 1 AND o.valid = 1
GROUP BY od.outbound_order_id, o.order_no, o.status;

-- 10. 创建视图用于查询入库单明细汇总
CREATE OR REPLACE VIEW `v_inbound_detail_summary` AS
SELECT
    id.inbound_order_id,
    i.order_no as inbound_order_no,
    i.status as inbound_status,
    COUNT(id.id) as detail_count,
    SUM(id.planned_quantity) as total_planned_quantity,
    SUM(id.actual_quantity) as total_actual_quantity,
    SUM(id.rollback_quantity) as total_rollback_quantity,
    GROUP_CONCAT(DISTINCT id.source_inbound_order_no) as source_order_nos
FROM t_inbound_order_detail id
LEFT JOIN t_inbound_order i ON id.inbound_order_id = i.id
WHERE id.valid = 1 AND i.valid = 1
GROUP BY id.inbound_order_id, i.order_no, i.status;
