<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>工作台</h2>
      <div class="refresh-time">
        <el-icon><Refresh /></el-icon>
        刷新时间
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stats-card">
        <div class="stats-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%)">
          <el-icon><Box /></el-icon>
        </div>
        <div class="stats-content">
          <h3>{{ statsData.customerOutbound }}</h3>
          <p>客户出库管理</p>
        </div>
      </div>
      
      <div class="stats-card">
        <div class="stats-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%)">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="stats-content">
          <h3>{{ statsData.downstreamStock }}</h3>
          <p>下游客户库存</p>
        </div>
      </div>
      
      <div class="stats-card">
        <div class="stats-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)">
          <el-icon><Truck /></el-icon>
        </div>
        <div class="stats-content">
          <h3>{{ statsData.logisticsManagement }}</h3>
          <p>物流承运管理</p>
        </div>
      </div>
    </div>
    
    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="section-header">
        <h3>今日计划</h3>
      </div>
      
      <!-- 表格标签页 -->
      <el-tabs v-model="activeTab" class="data-tabs">
        <el-tab-pane label="待处理下单" name="pending">
          <div class="table-container">
            <el-table :data="pendingOrders" style="width: 100%">
              <el-table-column prop="customerName" label="客户名称" />
              <el-table-column prop="customerName" label="客户名称" />
              <el-table-column prop="orderAmount" label="订单金额" />
              <el-table-column prop="orderQuantity" label="订单数量" />
              <el-table-column prop="status" label="状态" />
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="primary" size="small">处理</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="手动排班出库" name="manual">
          <div class="table-container">
            <el-table :data="manualOutbound" style="width: 100%">
              <el-table-column prop="warehouseName" label="仓库名称" />
              <el-table-column prop="customerName" label="客户名称" />
              <el-table-column prop="scheduledTime" label="排班时间" />
              <el-table-column prop="quantity" label="出库数量" />
              <el-table-column prop="operator" label="操作员" />
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="success" size="small">确认</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="今日出库" name="today">
          <div class="table-container">
            <el-table :data="todayOutbound" style="width: 100%">
              <el-table-column prop="outboundNo" label="出库单号" />
              <el-table-column prop="customerName" label="客户名称" />
              <el-table-column prop="quantity" label="出库数量" />
              <el-table-column prop="outboundTime" label="出库时间" />
              <el-table-column prop="status" label="状态" />
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="primary" size="small">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="未来出库" name="future">
          <div class="table-container">
            <el-table :data="futureOutbound" style="width: 100%">
              <el-table-column prop="plannedDate" label="计划日期" />
              <el-table-column prop="customerName" label="客户名称" />
              <el-table-column prop="quantity" label="预计数量" />
              <el-table-column prop="warehouseName" label="仓库名称" />
              <el-table-column prop="priority" label="优先级" />
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="warning" size="small">调整</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="周出计划" name="weekly">
          <div class="table-container">
            <el-table :data="weeklyPlan" style="width: 100%">
              <el-table-column prop="week" label="周次" />
              <el-table-column prop="totalQuantity" label="总计划量" />
              <el-table-column prop="completedQuantity" label="已完成量" />
              <el-table-column prop="remainingQuantity" label="剩余量" />
              <el-table-column prop="completionRate" label="完成率" />
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="info" size="small">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'

export default {
  name: 'Dashboard',
  setup() {
    const activeTab = ref('pending')
    
    // 统计数据
    const statsData = reactive({
      customerOutbound: 0,
      downstreamStock: 0,
      logisticsManagement: 27
    })
    
    // 待处理订单数据
    const pendingOrders = ref([
      {
        customerName: 'GC住宅公司A类光伏设备公司',
        orderAmount: '好多个千万万',
        orderQuantity: '好多个千万万111',
        status: '待处理'
      }
    ])
    
    // 手动排班出库数据
    const manualOutbound = ref([
      {
        warehouseName: 'GC住宅公司A类光伏设备公司',
        customerName: '好多个千万万',
        scheduledTime: '好多个千万万111',
        quantity: '上海市上海市松江区 区 区(松江区)松江区松江...',
        operator: '1'
      }
    ])
    
    // 今日出库数据
    const todayOutbound = ref([
      {
        outboundNo: 'GC住宅公司A类光伏设备公司',
        customerName: '好多个千万万',
        quantity: '好多个千万万111',
        outboundTime: '上海市上海市松江区 区 区(松江区)松江区松江...',
        status: '已出库'
      }
    ])
    
    // 未来出库数据
    const futureOutbound = ref([
      {
        plannedDate: 'GC住宅公司A类光伏设备公司',
        customerName: '好多个千万万',
        quantity: '好多个千万万111',
        warehouseName: '上海市上海市松江区 区 区(松江区)松江区松江...',
        priority: '高'
      }
    ])
    
    // 周出计划数据
    const weeklyPlan = ref([
      {
        week: '第31周',
        totalQuantity: '1000',
        completedQuantity: '800',
        remainingQuantity: '200',
        completionRate: '80%'
      }
    ])
    
    // 加载数据
    const loadData = () => {
      // 这里可以调用API获取真实数据
      console.log('加载工作台数据')
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      activeTab,
      statsData,
      pendingOrders,
      manualOutbound,
      todayOutbound,
      futureOutbound,
      weeklyPlan
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  min-height: calc(100vh - 60px);
  height: calc(100vh - 60px);
  background: #f0f2f5;
  padding: 20px;
  width: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.refresh-time {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.refresh-time .el-icon {
  margin-right: 5px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stats-card {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: #fff;
}

.stats-content h3 {
  margin: 0;
  font-size: 28px;
  color: #333;
  font-weight: bold;
}

.stats-content p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 20px;
  border-bottom: 1px solid #e6e6e6;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.data-tabs {
  padding: 0 20px;
}

.table-container {
  padding: 20px 0;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-table th) {
  background-color: #fafafa;
}
</style>
