package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.TResource;
import com.yi.mapper.TResourceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资源表 服务类
 */
@Service
public class TResourceService extends ServiceImpl<TResourceMapper, TResource> {

    @Autowired
    private TRoleResourceService tRoleResourceService;

    /**
     * 查询所有资源（树形结构）
     */
    public List<TResource> selectResourceTree(Integer resourceType, Integer status) {
        return baseMapper.selectResourceTree(resourceType, status);
    }

    /**
     * 根据父ID查询子资源
     */
    public List<TResource> selectByParentId(Long parentId) {
        return baseMapper.selectByParentId(parentId);
    }

    /**
     * 根据资源编码查询资源
     */
    public TResource selectByResourceCode(String resourceCode) {
        return baseMapper.selectByResourceCode(resourceCode);
    }

    /**
     * 根据用户ID查询用户拥有的资源
     */
    public List<TResource> selectByUserId(Long userId, Integer resourceType) {
        return baseMapper.selectByUserId(userId, resourceType);
    }

    /**
     * 根据角色ID查询角色拥有的资源
     */
    public List<TResource> selectByRoleId(Long roleId) {
        return baseMapper.selectByRoleId(roleId);
    }

    /**
     * 查询用户的菜单权限
     */
    public List<TResource> selectMenusByUserId(Long userId) {
        return baseMapper.selectMenusByUserId(userId);
    }

    /**
     * 查询用户的按钮权限
     */
    public List<TResource> selectButtonsByUserId(Long userId) {
        return baseMapper.selectButtonsByUserId(userId);
    }

    /**
     * 查询所有启用的资源
     */
    public List<TResource> selectEnabledResources() {
        return baseMapper.selectEnabledResources();
    }

    /**
     * 检查资源是否有子资源
     */
    public boolean hasChildren(Long resourceId) {
        return baseMapper.hasChildren(resourceId);
    }

    /**
     * 创建资源
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createResource(TResource resource) {
        // 检查资源编码是否存在
        if (checkResourceCodeExists(resource.getResourceCode(), null)) {
            throw new RuntimeException("资源编码已存在");
        }
        
        // 设置默认值
        resource.setStatus(1);
        resource.setValid(1);
        resource.setCreatedTime(LocalDateTime.now());
        resource.setLastModifiedTime(LocalDateTime.now());
        
        return save(resource);
    }

    /**
     * 更新资源
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateResource(TResource resource) {
        // 检查资源编码是否存在
        if (checkResourceCodeExists(resource.getResourceCode(), resource.getId())) {
            throw new RuntimeException("资源编码已存在");
        }
        
        resource.setLastModifiedTime(LocalDateTime.now());
        return updateById(resource);
    }

    /**
     * 删除资源
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteResource(Long resourceId) {
        // 检查是否有子资源
        if (hasChildren(resourceId)) {
            throw new RuntimeException("该资源下有子资源，无法删除");
        }
        
        // 删除角色资源关联
        tRoleResourceService.deleteByResourceId(resourceId);
        
        // 逻辑删除资源
        TResource resource = new TResource();
        resource.setId(resourceId);
        resource.setValid(0);
        resource.setLastModifiedTime(LocalDateTime.now());
        
        return updateById(resource);
    }

    /**
     * 启用/禁用资源
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateResourceStatus(Long resourceId, Integer status) {
        TResource resource = new TResource();
        resource.setId(resourceId);
        resource.setStatus(status);
        resource.setLastModifiedTime(LocalDateTime.now());
        return updateById(resource);
    }

    /**
     * 检查资源编码是否存在
     */
    public boolean checkResourceCodeExists(String resourceCode, Long excludeId) {
        LambdaQueryWrapper<TResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TResource::getResourceCode, resourceCode)
               .eq(TResource::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(TResource::getId, excludeId);
        }
        return count(wrapper) > 0;
    }
}
