package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.TUserRole;
import com.yi.mapper.TUserRoleMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户角色关联表 服务类
 */
@Service
public class TUserRoleService extends ServiceImpl<TUserRoleMapper, TUserRole> {

    /**
     * 根据用户ID删除用户角色关联
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByUserId(Long userId) {
        return baseMapper.deleteByUserId(userId);
    }

    /**
     * 根据角色ID删除用户角色关联
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByRoleId(Long roleId) {
        return baseMapper.deleteByRoleId(roleId);
    }

    /**
     * 批量插入用户角色关联
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<TUserRole> userRoles) {
        if (CollectionUtils.isEmpty(userRoles)) {
            return 0;
        }
        
        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        userRoles.forEach(userRole -> {
            userRole.setCreatedTime(now);
        });
        
        return baseMapper.batchInsert(userRoles);
    }

    /**
     * 根据用户ID查询角色ID列表
     */
    public List<Long> selectRoleIdsByUserId(Long userId) {
        return baseMapper.selectRoleIdsByUserId(userId);
    }

    /**
     * 根据角色ID查询用户ID列表
     */
    public List<Long> selectUserIdsByRoleId(Long roleId) {
        return baseMapper.selectUserIdsByRoleId(roleId);
    }

    /**
     * 检查用户是否拥有指定角色
     */
    public boolean existsByUserIdAndRoleId(Long userId, Long roleId) {
        LambdaQueryWrapper<TUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TUserRole::getUserId, userId)
               .eq(TUserRole::getRoleId, roleId);
        return count(wrapper) > 0;
    }
}
