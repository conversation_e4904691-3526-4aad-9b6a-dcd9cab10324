<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="yi_mangement">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>exact</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <ServerVersion>8.0.42</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="mysql">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="ganxian_taxi">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="293" parent="1" name="dmarket_db">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="294" parent="1" name="yi_mangement">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <Current>1</Current>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-07-31.15:11:47</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <user id="295" parent="1" name="root">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="296" parent="1" name="steamshop">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="297" parent="1" name="zhouc"/>
    <user id="298" parent="1" name="debian-sys-maint">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="299" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="300" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="301" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="302" parent="1" name="root">
      <Host>localhost</Host>
      <Plugin>auth_socket</Plugin>
    </user>
    <table id="303" parent="294" name="t_cloud_warehouse">
      <Comment>云仓表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="304" parent="294" name="t_contract">
      <Comment>合同管理表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="305" parent="294" name="t_contract_log">
      <Comment>合同操作日志表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="306" parent="294" name="t_contract_sku">
      <Comment>合同SKU明细表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="307" parent="294" name="t_customer_company">
      <Comment>客户公司表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="308" parent="294" name="t_customer_relate_downstream_address">
      <Comment>客户下游地址关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="309" parent="294" name="t_customer_warehouse">
      <Comment>客户仓库表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="310" parent="294" name="t_general_file">
      <Comment>通用文件表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="311" parent="294" name="t_resource">
      <Comment>资源表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="312" parent="294" name="t_role">
      <Comment>角色表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="313" parent="294" name="t_role_resource">
      <Comment>角色资源关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="314" parent="294" name="t_shipping_demand">
      <Comment>发货需求表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="315" parent="294" name="t_shipping_order">
      <Comment>发运订单表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="316" parent="294" name="t_shipping_order_log">
      <Comment>发运订单日志表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="317" parent="294" name="t_sku">
      <Comment>SKU表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="318" parent="294" name="t_supplier">
      <Comment>供应商表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="319" parent="294" name="t_supplier_warehouse">
      <Comment>供应商仓库表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="320" parent="294" name="t_user">
      <Comment>用户表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="321" parent="294" name="t_user_role">
      <Comment>用户角色关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="322" parent="294" name="t_warehouse_sku">
      <Comment>仓库SKU关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <column id="323" parent="303" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="324" parent="303" name="warehouse_name">
      <Comment>仓库名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="325" parent="303" name="warehouse_type">
      <Comment>仓库类型：1-中心仓，2-卫星仓，3-虚拟仓</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="326" parent="303" name="warehouse_attribute">
      <Comment>仓库属性：1-自有，2-第三方</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="327" parent="303" name="province_id">
      <Comment>省份ID</Comment>
      <Position>5</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="328" parent="303" name="province_name">
      <Comment>省份名称</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="329" parent="303" name="city_id">
      <Comment>城市ID</Comment>
      <Position>7</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="330" parent="303" name="city_name">
      <Comment>城市名称</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="331" parent="303" name="area_id">
      <Comment>区县ID</Comment>
      <Position>9</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="332" parent="303" name="area_name">
      <Comment>区县名称</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="333" parent="303" name="detailed_address">
      <Comment>地址详情</Comment>
      <Position>11</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="334" parent="303" name="contact_person">
      <Comment>联系人</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="335" parent="303" name="contact_phone">
      <Comment>联系方式</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="336" parent="303" name="working_hours">
      <Comment>作业时间</Comment>
      <Position>14</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="337" parent="303" name="enabled">
      <Comment>启用状态：1-启用，0-禁用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="338" parent="303" name="remark">
      <Comment>备注</Comment>
      <Position>16</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="339" parent="303" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="340" parent="303" name="created_by">
      <Comment>创建人</Comment>
      <Position>18</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="341" parent="303" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>19</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="342" parent="303" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>20</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="343" parent="303" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>21</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="344" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="345" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="346" parent="304" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="347" parent="304" name="contract_no">
      <Comment>合同编号（唯一）</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="348" parent="304" name="out_customer_company_id">
      <Comment>我司主体</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="349" parent="304" name="customer_company_id">
      <Comment>客户主体ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="350" parent="304" name="contract_status">
      <Comment>合同状态：1-待生效，2-生效中，3-已到期，4-已作废</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="351" parent="304" name="archive_status">
      <Comment>归档状态：1-待归档，2-已归档</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="352" parent="304" name="effective_date">
      <Comment>合同生效日期</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="353" parent="304" name="expiry_date">
      <Comment>合同失效日期</Comment>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="354" parent="304" name="cancel_reason">
      <Comment>取消原因</Comment>
      <Position>9</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="355" parent="304" name="remark">
      <Comment>备注信息</Comment>
      <Position>10</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="356" parent="304" name="created_by">
      <Comment>创建人</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="357" parent="304" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="358" parent="304" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="359" parent="304" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="360" parent="304" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="361" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="362" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="363" parent="305" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="364" parent="305" name="contract_id">
      <Comment>合同ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="365" parent="305" name="action">
      <Comment>操作动作：1-编辑合同，2-作废合同</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="366" parent="305" name="operator">
      <Comment>操作人</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="367" parent="305" name="operation_time">
      <Comment>操作时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="368" parent="305" name="remark">
      <Comment>备注信息</Comment>
      <Position>6</Position>
      <StoredType>varchar(1000)|0s</StoredType>
    </column>
    <column id="369" parent="305" name="created_by">
      <Comment>创建人</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="370" parent="305" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="371" parent="305" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="372" parent="305" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="373" parent="305" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="374" parent="305" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="375" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="376" parent="306" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="377" parent="306" name="contract_id">
      <Comment>合同ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="378" parent="306" name="first_category">
      <Comment>一级类目 1:共享托盘</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="379" parent="306" name="business_mode">
      <Comment>业务模式 1:静态租赁 2:租赁+流转 3:一口价</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="380" parent="306" name="created_by">
      <Comment>创建人</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="381" parent="306" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="382" parent="306" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="383" parent="306" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="384" parent="306" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="385" parent="306" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="386" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="387" parent="307" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="388" parent="307" name="customer_company_type">
      <Comment>客户类型 1合约客户 2非合约客户</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="389" parent="307" name="company_name">
      <Comment>公司名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="390" parent="307" name="province_id">
      <Comment>省份ID</Comment>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="391" parent="307" name="province_name">
      <Comment>省份名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="392" parent="307" name="city_id">
      <Comment>城市ID</Comment>
      <Position>6</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="393" parent="307" name="city_name">
      <Comment>城市名称</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="394" parent="307" name="area_id">
      <Comment>区县ID</Comment>
      <Position>8</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="395" parent="307" name="area_name">
      <Comment>区县名称</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="396" parent="307" name="detailed_address">
      <Comment>详细地址</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="397" parent="307" name="contact_person">
      <Comment>联系人</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="398" parent="307" name="contact_phone">
      <Comment>联系方式</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="399" parent="307" name="invoice_type">
      <Comment>增值税开票发票资料：1-后补，2-与客户公司一致</Comment>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="400" parent="307" name="invoice_company_name">
      <Comment>开票公司名称</Comment>
      <Position>14</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="401" parent="307" name="tax_number">
      <Comment>税号</Comment>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="402" parent="307" name="invoice_contact_person">
      <Comment>发票联系人</Comment>
      <Position>16</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="403" parent="307" name="invoice_mobile">
      <Comment>发票联系手机号</Comment>
      <Position>17</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="404" parent="307" name="invoice_email">
      <Comment>发票邮箱</Comment>
      <Position>18</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="405" parent="307" name="bank_name">
      <Comment>开户银行名称</Comment>
      <Position>19</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="406" parent="307" name="bank_account">
      <Comment>银行账号</Comment>
      <Position>20</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="407" parent="307" name="enabled">
      <Comment>启用状态：1-启用，0-禁用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="408" parent="307" name="created_by">
      <Comment>创建人</Comment>
      <Position>22</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="409" parent="307" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>23</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="410" parent="307" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>24</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="411" parent="307" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>25</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="412" parent="307" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>26</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="413" parent="307" name="remark">
      <Comment>备注</Comment>
      <Position>27</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="414" parent="307" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="415" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="416" parent="308" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="417" parent="308" name="company_id">
      <Comment>公司ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="418" parent="308" name="downstream_customer_company_id">
      <Comment>下游客户ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="419" parent="308" name="warehouse_id">
      <Comment>下游客户仓库ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="420" parent="308" name="enabled">
      <Comment>启用状态：1-启用，0-禁用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="421" parent="308" name="created_by">
      <Comment>创建人</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="422" parent="308" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="423" parent="308" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="424" parent="308" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="425" parent="308" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="426" parent="308" name="remark">
      <Comment>备注</Comment>
      <Position>11</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="427" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="428" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="429" parent="309" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="430" parent="309" name="company_id">
      <Comment>公司ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="431" parent="309" name="warehouse_name">
      <Comment>仓库名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="432" parent="309" name="province_id">
      <Comment>省份ID</Comment>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="433" parent="309" name="province_name">
      <Comment>省份名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="434" parent="309" name="city_id">
      <Comment>城市ID</Comment>
      <Position>6</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="435" parent="309" name="city_name">
      <Comment>城市名称</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="436" parent="309" name="area_id">
      <Comment>区县ID</Comment>
      <Position>8</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="437" parent="309" name="area_name">
      <Comment>区县名称</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="438" parent="309" name="detailed_address">
      <Comment>详细地址</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="439" parent="309" name="contact_person">
      <Comment>收货人</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="440" parent="309" name="mobile_phone">
      <Comment>手机号</Comment>
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="441" parent="309" name="landline_phone">
      <Comment>座机号</Comment>
      <Position>13</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="442" parent="309" name="enabled">
      <Comment>启用状态：1-启用，0-禁用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="443" parent="309" name="created_by">
      <Comment>创建人</Comment>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="444" parent="309" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="445" parent="309" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="446" parent="309" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="447" parent="309" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="448" parent="309" name="remark">
      <Comment>备注</Comment>
      <Position>20</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="449" parent="309" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="450" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="451" parent="310" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="452" parent="310" name="type">
      <Comment>类型: 1.销售合同, 2.发运订单</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="453" parent="310" name="file_type">
      <Comment>文件类型</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="454" parent="310" name="related_id">
      <Comment>关联Id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="455" parent="310" name="file_path">
      <Comment>文件路径</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>varchar(300)|0s</StoredType>
    </column>
    <column id="456" parent="310" name="created_by">
      <Comment>创建人</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="457" parent="310" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="458" parent="310" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="459" parent="310" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="460" parent="310" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="461" parent="310" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="462" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="463" parent="311" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>资源ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="464" parent="311" name="parent_id">
      <Comment>父资源ID</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="465" parent="311" name="resource_code">
      <Comment>资源编码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="466" parent="311" name="resource_name">
      <Comment>资源名称</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="467" parent="311" name="resource_type">
      <Comment>资源类型：1-目录，2-菜单，3-按钮，4-接口</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="468" parent="311" name="path">
      <Comment>路由地址</Comment>
      <Position>6</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="469" parent="311" name="component">
      <Comment>组件路径</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="470" parent="311" name="perms">
      <Comment>权限标识</Comment>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="471" parent="311" name="icon">
      <Comment>图标</Comment>
      <Position>9</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="472" parent="311" name="order_num">
      <Comment>显示顺序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="473" parent="311" name="visible">
      <Comment>是否显示：0-隐藏，1-显示</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>11</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="474" parent="311" name="status">
      <Comment>状态：0-禁用，1-启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="475" parent="311" name="created_by">
      <Comment>创建人</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="476" parent="311" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="477" parent="311" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="478" parent="311" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="479" parent="311" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="480" parent="311" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="481" parent="311" name="uk_resource_code">
      <ColNames>resource_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="482" parent="311" name="idx_parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="483" parent="311" name="idx_resource_type">
      <ColNames>resource_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="484" parent="311" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="485" parent="311" name="idx_valid">
      <ColNames>valid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="486" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="487" parent="311" name="uk_resource_code">
      <UnderlyingIndexName>uk_resource_code</UnderlyingIndexName>
    </key>
    <column id="488" parent="312" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="489" parent="312" name="role_code">
      <Comment>角色编码</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="490" parent="312" name="role_name">
      <Comment>角色名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="491" parent="312" name="role_desc">
      <Comment>角色描述</Comment>
      <Position>4</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="492" parent="312" name="role_sort">
      <Comment>显示顺序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="493" parent="312" name="data_scope">
      <Comment>数据范围：1-全部数据权限，2-自定数据权限，3-本部门数据权限，4-本部门及以下数据权限，5-仅本人数据权限</Comment>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="494" parent="312" name="status">
      <Comment>状态：0-禁用，1-启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="495" parent="312" name="created_by">
      <Comment>创建人</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="496" parent="312" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="497" parent="312" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="498" parent="312" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="499" parent="312" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="500" parent="312" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="501" parent="312" name="uk_role_code">
      <ColNames>role_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="502" parent="312" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="503" parent="312" name="idx_valid">
      <ColNames>valid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="504" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="505" parent="312" name="uk_role_code">
      <UnderlyingIndexName>uk_role_code</UnderlyingIndexName>
    </key>
    <column id="506" parent="313" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="507" parent="313" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="508" parent="313" name="resource_id">
      <Comment>资源ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="509" parent="313" name="created_by">
      <Comment>创建人</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="510" parent="313" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="511" parent="313" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="512" parent="313" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="513" parent="313" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="514" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="515" parent="313" name="uk_role_resource">
      <ColNames>role_id
resource_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="516" parent="313" name="idx_role_id">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="517" parent="313" name="idx_resource_id">
      <ColNames>resource_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="518" parent="313" name="idx_valid">
      <ColNames>valid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="519" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="520" parent="313" name="uk_role_resource">
      <UnderlyingIndexName>uk_role_resource</UnderlyingIndexName>
    </key>
    <column id="521" parent="314" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="522" parent="314" name="order_no">
      <Comment>发运订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="523" parent="314" name="status">
      <Comment>发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消</Comment>
      <DefaultExpression>1000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="524" parent="314" name="customer_company_id">
      <Comment>客户公司ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="525" parent="314" name="warehouse_id">
      <Comment>收货仓库ID</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="526" parent="314" name="first_category">
      <Comment>一级类目 1:共享托盘</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="527" parent="314" name="second_category">
      <Comment>二级类目</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="528" parent="314" name="demand_quantity">
      <Comment>需求数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="529" parent="314" name="pending_quantity">
      <Comment>待确认数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="530" parent="314" name="shipped_quantity">
      <Comment>发货数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="531" parent="314" name="unexecuted_quantity">
      <Comment>未执行数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="532" parent="314" name="demand_time">
      <Comment>需求时间</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="533" parent="314" name="remark">
      <Comment>备注</Comment>
      <Position>13</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="534" parent="314" name="created_by">
      <Comment>创建人</Comment>
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="535" parent="314" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="536" parent="314" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>16</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="537" parent="314" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="538" parent="314" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="539" parent="314" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="540" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="541" parent="315" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="542" parent="315" name="order_no">
      <Comment>订单号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="543" parent="315" name="status">
      <Comment>订单状态：1000-待发货，2000-发货中，3000-已完结，4000-已取消</Comment>
      <DefaultExpression>1000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="544" parent="315" name="contract_code">
      <Comment>合同编号</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="545" parent="315" name="customer_company_id">
      <Comment>客户公司ID</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="546" parent="315" name="warehouse_id">
      <Comment>收货仓库ID</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="547" parent="315" name="first_category">
      <Comment>一级类目 1:共享托盘</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="548" parent="315" name="second_category">
      <Comment>二级类目</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="549" parent="315" name="count">
      <Comment>需求数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="550" parent="315" name="shipped_quantity">
      <Comment>发货数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="551" parent="315" name="received_quantity">
      <Comment>签收数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="552" parent="315" name="demand_time">
      <Comment>需求时间</Comment>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="553" parent="315" name="cancel_reason">
      <Comment>取消原因</Comment>
      <Position>13</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="554" parent="315" name="created_by">
      <Comment>创建人</Comment>
      <Position>14</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="555" parent="315" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="556" parent="315" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>16</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="557" parent="315" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="558" parent="315" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="559" parent="315" name="remark">
      <Comment>备注</Comment>
      <Position>19</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="560" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="561" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="562" parent="316" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="563" parent="316" name="order_id">
      <Comment>订单ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="564" parent="316" name="action">
      <Comment>动作：1000-取消订单，2000-完结订单</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="565" parent="316" name="operator">
      <Comment>操作人</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="566" parent="316" name="operate_time">
      <Comment>操作时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="567" parent="316" name="remark">
      <Comment>备注</Comment>
      <Position>6</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="568" parent="316" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="569" parent="316" name="idx_order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="570" parent="316" name="idx_operate_time">
      <ColNames>operate_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="571" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="572" parent="317" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="573" parent="317" name="first_category">
      <Comment>一级类目 1:共享托盘</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="574" parent="317" name="second_category">
      <Comment>二级类目</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="575" parent="317" name="third_category">
      <Comment>三级类目</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="576" parent="317" name="length">
      <Comment>长度(mm)</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="577" parent="317" name="width">
      <Comment>宽度(mm)</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="578" parent="317" name="height">
      <Comment>高度(mm)</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="579" parent="317" name="weight">
      <Comment>重量(Kg)</Comment>
      <Position>8</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="580" parent="317" name="remark">
      <Comment>备注</Comment>
      <Position>9</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="581" parent="317" name="enabled">
      <Comment>启用状态：1-启用，0-禁用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <column id="582" parent="317" name="created_by">
      <Comment>创建人</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="583" parent="317" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="584" parent="317" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="585" parent="317" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="586" parent="317" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
      <StoredType>tinyint|0s</StoredType>
    </column>
    <index id="587" parent="317" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="588" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="589" parent="318" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="590" parent="318" name="supplier_name">
      <Comment>供应商名称</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="591" parent="318" name="contact_person">
      <Comment>联系人</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="592" parent="318" name="contact_phone">
      <Comment>联系方式</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="593" parent="318" name="bank_name">
      <Comment>开户行</Comment>
      <Position>5</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="594" parent="318" name="bank_account">
      <Comment>银行账号</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="595" parent="318" name="remark">
      <Comment>备注</Comment>
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="596" parent="318" name="enabled">
      <Comment>启用状态：1-启用，0-禁用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="597" parent="318" name="created_by">
      <Comment>创建人</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="598" parent="318" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="599" parent="318" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="600" parent="318" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="601" parent="318" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="602" parent="318" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="603" parent="318" name="idx_supplier_name">
      <ColNames>supplier_name</ColNames>
      <Comment>供应商名称索引</Comment>
      <Type>btree</Type>
    </index>
    <index id="604" parent="318" name="idx_enabled_valid">
      <ColNames>enabled
valid</ColNames>
      <Comment>启用状态和有效性复合索引</Comment>
      <Type>btree</Type>
    </index>
    <key id="605" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="606" parent="319" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="607" parent="319" name="supplier_id">
      <Comment>供应商ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="608" parent="319" name="warehouse_name">
      <Comment>仓库名称</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="609" parent="319" name="province_id">
      <Comment>省份ID</Comment>
      <Position>4</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="610" parent="319" name="province_name">
      <Comment>省份名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="611" parent="319" name="city_id">
      <Comment>城市ID</Comment>
      <Position>6</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="612" parent="319" name="city_name">
      <Comment>城市名称</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="613" parent="319" name="area_id">
      <Comment>区县ID</Comment>
      <Position>8</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="614" parent="319" name="area_name">
      <Comment>区县名称</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="615" parent="319" name="detailed_address">
      <Comment>地址详情</Comment>
      <Position>10</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="616" parent="319" name="contact_person">
      <Comment>联系人</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="617" parent="319" name="contact_phone">
      <Comment>联系方式</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="618" parent="319" name="enabled">
      <Comment>启用状态：1-启用，0-禁用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="619" parent="319" name="remark">
      <Comment>备注</Comment>
      <Position>14</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="620" parent="319" name="created_by">
      <Comment>创建人</Comment>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="621" parent="319" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="622" parent="319" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>17</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="623" parent="319" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="624" parent="319" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <index id="625" parent="319" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="626" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="627" parent="320" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="628" parent="320" name="username">
      <Comment>用户名</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="629" parent="320" name="password">
      <Comment>密码</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="630" parent="320" name="real_name">
      <Comment>真实姓名</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="631" parent="320" name="email">
      <Comment>邮箱</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="632" parent="320" name="phone">
      <Comment>手机号</Comment>
      <Position>6</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="633" parent="320" name="avatar">
      <Comment>头像地址</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="634" parent="320" name="gender">
      <Comment>性别：0-女，1-男</Comment>
      <Position>8</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="635" parent="320" name="birthday">
      <Comment>生日</Comment>
      <Position>9</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="636" parent="320" name="status">
      <Comment>状态：0-禁用，1-启用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="637" parent="320" name="login_ip">
      <Comment>最后登录IP</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="638" parent="320" name="login_time">
      <Comment>最后登录时间</Comment>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="639" parent="320" name="created_by">
      <Comment>创建人</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="640" parent="320" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="641" parent="320" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>15</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="642" parent="320" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="643" parent="320" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="644" parent="320" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="645" parent="320" name="uk_username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="646" parent="320" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="647" parent="320" name="idx_valid">
      <ColNames>valid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="648" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="649" parent="320" name="uk_username">
      <UnderlyingIndexName>uk_username</UnderlyingIndexName>
    </key>
    <column id="650" parent="321" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="651" parent="321" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="652" parent="321" name="role_id">
      <Comment>角色ID</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="653" parent="321" name="created_by">
      <Comment>创建人</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="654" parent="321" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="655" parent="321" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="656" parent="321" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="657" parent="321" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <index id="658" parent="321" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="659" parent="321" name="uk_user_role">
      <ColNames>user_id
role_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="660" parent="321" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="661" parent="321" name="idx_role_id">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="662" parent="321" name="idx_valid">
      <ColNames>valid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="663" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="664" parent="321" name="uk_user_role">
      <UnderlyingIndexName>uk_user_role</UnderlyingIndexName>
    </key>
    <column id="665" parent="322" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="666" parent="322" name="warehouse_id">
      <Comment>仓库ID</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>bigint|0s</StoredType>
    </column>
    <column id="667" parent="322" name="first_category">
      <Comment>一级类目 1:共享托盘</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="668" parent="322" name="second_category">
      <Comment>二级类目</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="669" parent="322" name="enabled">
      <Comment>启用状态：1-启用，0-禁用</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="670" parent="322" name="created_by">
      <Comment>创建人</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="671" parent="322" name="created_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="672" parent="322" name="last_modified_by">
      <Comment>最后修改人</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="673" parent="322" name="last_modified_time">
      <Comment>最后修改时间</Comment>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="674" parent="322" name="valid">
      <Comment>有效性：1-有效，0-无效</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="675" parent="322" name="remark">
      <Comment>备注</Comment>
      <Position>11</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <index id="676" parent="322" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="677" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>