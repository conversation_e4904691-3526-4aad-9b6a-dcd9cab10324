<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2795f283-96d6-40c0-8817-20d448c6151e" name="更改" comment="新增云仓接口">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yi/configuration/web/WebConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yi/configuration/web/WebConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/yi/utils/BcryptUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/yi/utils/BcryptUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/sql/shipping-order-add-fields.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/sql/shipping-order-constraints.sql" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\zhouc\maven\apache-maven-3.9.11\repo" />
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
        <option name="userSettingsFile" value="D:\zhouc\maven\apache-maven-3.9.11\apache-maven-3.9.11\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30MmO1TnBCiDvQkmK3ZItwGEITA" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.YiManagementApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/zhouc/project/yelopack/yi-management&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;SQL Dialects&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="YiManagementApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="Yi_Management" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yi.YiManagementApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="2795f283-96d6-40c0-8817-20d448c6151e" name="更改" comment="" />
      <created>1753445779705</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753445779705</updated>
      <workItem from="1753445780857" duration="14677000" />
      <workItem from="1753528356578" duration="27821000" />
      <workItem from="1753708145324" duration="6389000" />
      <workItem from="1753790400343" duration="1965000" />
      <workItem from="1753795420426" duration="750000" />
      <workItem from="1753797664541" duration="267000" />
      <workItem from="1753798047272" duration="204000" />
      <workItem from="1753798390751" duration="5486000" />
      <workItem from="1753862807114" duration="20534000" />
    </task>
    <task id="LOCAL-00001" summary="新增合同相关接口">
      <option name="closed" value="true" />
      <created>1753534302941</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753534302941</updated>
    </task>
    <task id="LOCAL-00002" summary="新增合同相关接口">
      <option name="closed" value="true" />
      <created>1753534671256</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753534671256</updated>
    </task>
    <task id="LOCAL-00003" summary="新增合同相关接口">
      <option name="closed" value="true" />
      <created>1753540612312</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753540612312</updated>
    </task>
    <task id="LOCAL-00004" summary="新增发运订单接口">
      <option name="closed" value="true" />
      <created>1753544955083</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753544955083</updated>
    </task>
    <task id="LOCAL-00005" summary="新增发运订单接口">
      <option name="closed" value="true" />
      <created>1753619714617</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753619714617</updated>
    </task>
    <task id="LOCAL-00006" summary="新增发运订单接口">
      <option name="closed" value="true" />
      <created>1753635921826</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753635921826</updated>
    </task>
    <task id="LOCAL-00007" summary="新增发货需求接口">
      <option name="closed" value="true" />
      <created>1753715376329</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753715376329</updated>
    </task>
    <task id="LOCAL-00008" summary="新增发货需求接口">
      <option name="closed" value="true" />
      <created>1753800571489</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753800571489</updated>
    </task>
    <task id="LOCAL-00009" summary="新增发货需求接口">
      <option name="closed" value="true" />
      <created>1753802482614</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753802482615</updated>
    </task>
    <task id="LOCAL-00010" summary="新增供应商接口">
      <option name="closed" value="true" />
      <created>1753803934710</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753803934710</updated>
    </task>
    <task id="LOCAL-00011" summary="新增供应商接口">
      <option name="closed" value="true" />
      <created>1753871149499</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753871149499</updated>
    </task>
    <task id="LOCAL-00012" summary="新增供应商仓库接口">
      <option name="closed" value="true" />
      <created>1753875518326</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753875518326</updated>
    </task>
    <task id="LOCAL-00013" summary="新增云仓接口">
      <option name="closed" value="true" />
      <created>1753879835461</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753879835461</updated>
    </task>
    <option name="localTasksCounter" value="14" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="新增合同相关接口" />
    <MESSAGE value="新增发运订单接口" />
    <MESSAGE value="新增发货需求接口" />
    <MESSAGE value="新增供应商接口" />
    <MESSAGE value="新增供应商仓库接口" />
    <MESSAGE value="新增云仓接口" />
    <option name="LAST_COMMIT_MESSAGE" value="新增云仓接口" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>