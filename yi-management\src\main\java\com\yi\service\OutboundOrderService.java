package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.OutboundOrder;
import com.yi.enums.OutboundStatusEnum;
import com.yi.mapper.OutboundOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 出库单表 服务类
 */
@Service
public class OutboundOrderService extends ServiceImpl<OutboundOrderMapper, OutboundOrder> {

    @Autowired
    private OrderNumberService orderNumberService;

    /**
     * 分页查询出库单列表
     */
    public IPage<OutboundOrder> selectOutboundOrderPage(Page<OutboundOrder> page, String orderNo, Integer status,
                                                        Integer outboundType, Long outboundCompanyId, Long receiveCompanyId,
                                                        Integer firstCategory, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectOutboundOrderPage(page, orderNo, status, outboundType, outboundCompanyId,
                receiveCompanyId, firstCategory, startTime, endTime);
    }

    /**
     * 根据出库单号查询出库单
     */
    public OutboundOrder selectByOrderNo(String orderNo) {
        return baseMapper.selectByOrderNo(orderNo);
    }

    /**
     * 根据状态查询出库单列表
     */
    public List<OutboundOrder> selectByStatus(Integer status) {
        return baseMapper.selectByStatus(status);
    }

    /**
     * 创建出库单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createOutboundOrder(OutboundOrder outboundOrder) {
        // 生成出库单号
        if (!StringUtils.hasText(outboundOrder.getOrderNo())) {
            String orderNo = orderNumberService.generateOutboundOrderNo();
            outboundOrder.setOrderNo(orderNo);
        }

        // 检查单号是否已存在
        if (checkOrderNoExists(outboundOrder.getOrderNo(), null)) {
            throw new RuntimeException("出库单号已存在");
        }

        // 设置默认值
        if (outboundOrder.getStatus() == null) {
            outboundOrder.setStatus(OutboundStatusEnum.PENDING.getCode());
        }
        if (outboundOrder.getActualQuantity() == null) {
            outboundOrder.setActualQuantity(0);
        }
        outboundOrder.setValid(1);
        outboundOrder.setCreatedTime(LocalDateTime.now());
        outboundOrder.setLastModifiedTime(LocalDateTime.now());

        return save(outboundOrder);
    }

    /**
     * 更新出库单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOutboundOrder(OutboundOrder outboundOrder) {
        // 检查单号是否已存在（排除自己）
        if (StringUtils.hasText(outboundOrder.getOrderNo()) &&
            checkOrderNoExists(outboundOrder.getOrderNo(), outboundOrder.getId())) {
            throw new RuntimeException("出库单号已存在");
        }

        outboundOrder.setLastModifiedTime(LocalDateTime.now());
        return updateById(outboundOrder);
    }

    /**
     * 删除出库单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOutboundOrder(Long id) {
        OutboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 只有待出库状态的订单才能删除
        if (!OutboundStatusEnum.PENDING.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有待出库状态的订单才能删除");
        }

        // 逻辑删除
        OutboundOrder updateOrder = new OutboundOrder();
        updateOrder.setId(id);
        updateOrder.setValid(0);
        updateOrder.setLastModifiedTime(LocalDateTime.now());
        return updateById(updateOrder);
    }

    /**
     * 批量删除出库单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOutboundOrders(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        for (Long id : ids) {
            deleteOutboundOrder(id);
        }
        return true;
    }

    /**
     * 确认出库（更新状态为运输中）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmOutbound(Long id, Integer actualQuantity, String lastModifiedBy) {
        OutboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 只有待出库状态的订单才能确认出库
        if (!OutboundStatusEnum.PENDING.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有待出库状态的订单才能确认出库");
        }

        // 更新状态为运输中
        return baseMapper.updateStatus(id, OutboundStatusEnum.IN_TRANSIT.getCode(),
                actualQuantity, LocalDateTime.now(), lastModifiedBy) > 0;
    }

    /**
     * 完成出库（更新状态为已出库）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean completeOutbound(Long id, String lastModifiedBy) {
        OutboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 只有运输中状态的订单才能完成出库
        if (!OutboundStatusEnum.IN_TRANSIT.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有运输中状态的订单才能完成出库");
        }

        // 更新状态为已出库
        return baseMapper.updateStatus(id, OutboundStatusEnum.COMPLETED.getCode(),
                null, null, lastModifiedBy) > 0;
    }

    /**
     * 取消出库（回退到待出库状态）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOutbound(Long id, String lastModifiedBy) {
        OutboundOrder order = getById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 只有运输中状态的订单才能取消
        if (!OutboundStatusEnum.IN_TRANSIT.getCode().equals(order.getStatus())) {
            throw new RuntimeException("只有运输中状态的订单才能取消");
        }

        // 回退到待出库状态，清空实际出库数和出库时间
        return baseMapper.updateStatus(id, OutboundStatusEnum.PENDING.getCode(),
                0, null, lastModifiedBy) > 0;
    }

    /**
     * 统计各状态的出库单数量
     */
    public List<Map<String, Object>> getStatusStatistics() {
        return baseMapper.selectStatusStatistics();
    }

    /**
     * 统计各类型的出库单数量
     */
    public List<Map<String, Object>> getTypeStatistics() {
        return baseMapper.selectTypeStatistics();
    }

    /**
     * 查询待出库的订单
     */
    public List<OutboundOrder> getPendingOrders() {
        return baseMapper.selectPendingOrders();
    }

    /**
     * 查询运输中的订单
     */
    public List<OutboundOrder> getInTransitOrders() {
        return baseMapper.selectInTransitOrders();
    }

    /**
     * 检查出库单号是否存在
     */
    public boolean checkOrderNoExists(String orderNo, Long excludeId) {
        LambdaQueryWrapper<OutboundOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OutboundOrder::getOrderNo, orderNo)
               .eq(OutboundOrder::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(OutboundOrder::getId, excludeId);
        }
        return count(wrapper) > 0;
    }
}
