<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TResource">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="resource_code" property="resourceCode" />
        <result column="resource_name" property="resourceName" />
        <result column="resource_type" property="resourceType" />
        <result column="path" property="path" />
        <result column="component" property="component" />
        <result column="perms" property="perms" />
        <result column="icon" property="icon" />
        <result column="order_num" property="orderNum" />
        <result column="visible" property="visible" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, resource_code, resource_name, resource_type, path, component, perms, icon,
        order_num, visible, status, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <!-- 查询所有资源（树形结构） -->
    <select id="selectResourceTree" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_resource
        WHERE valid = 1
        <if test="resourceType != null">
            AND resource_type = #{resourceType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY order_num ASC, created_time ASC
    </select>

    <!-- 根据父ID查询子资源 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_resource
        WHERE parent_id = #{parentId} AND valid = 1
        ORDER BY order_num ASC, created_time ASC
    </select>

    <!-- 根据资源编码查询资源 -->
    <select id="selectByResourceCode" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_resource
        WHERE resource_code = #{resourceCode} AND valid = 1
    </select>

    <!-- 根据用户ID查询用户拥有的资源 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT
            r.id, r.parent_id, r.resource_code, r.resource_name, r.resource_type, r.path, r.component, r.perms, r.icon,
            r.order_num, r.visible, r.status, r.created_by, r.created_time, r.last_modified_by, r.last_modified_time, r.valid
        FROM t_resource r
        INNER JOIN t_role_resource rr ON r.id = rr.resource_id
        INNER JOIN t_user_role ur ON rr.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.valid = 1 AND rr.valid = 1 AND ur.valid = 1
          AND r.status = 1
        <if test="resourceType != null">
            AND r.resource_type = #{resourceType}
        </if>
        ORDER BY r.order_num ASC, r.created_time ASC
    </select>

    <!-- 根据角色ID查询角色拥有的资源 -->
    <select id="selectByRoleId" resultMap="BaseResultMap">
        SELECT
            r.id, r.parent_id, r.resource_code, r.resource_name, r.resource_type, r.path, r.component, r.perms, r.icon,
            r.order_num, r.visible, r.status, r.created_by, r.created_time, r.last_modified_by, r.last_modified_time, r.valid
        FROM t_resource r
        INNER JOIN t_role_resource rr ON r.id = rr.resource_id
        WHERE rr.role_id = #{roleId} AND r.valid = 1 AND rr.valid = 1
        ORDER BY r.order_num ASC, r.created_time ASC
    </select>

    <!-- 查询用户的菜单权限 -->
    <select id="selectMenusByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT
            r.id, r.parent_id, r.resource_code, r.resource_name, r.resource_type, r.path, r.component, r.perms, r.icon,
            r.order_num, r.visible, r.status, r.created_by, r.created_time, r.last_modified_by, r.last_modified_time, r.valid
        FROM t_resource r
        INNER JOIN t_role_resource rr ON r.id = rr.resource_id
        INNER JOIN t_user_role ur ON rr.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.valid = 1 AND rr.valid = 1 AND ur.valid = 1
          AND r.status = 1 AND r.visible = 1
          AND r.resource_type IN (1, 2)
        ORDER BY r.order_num ASC, r.created_time ASC
    </select>

    <!-- 查询用户的按钮权限 -->
    <select id="selectButtonsByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT
            r.id, r.parent_id, r.resource_code, r.resource_name, r.resource_type, r.path, r.component, r.perms, r.icon,
            r.order_num, r.visible, r.status, r.created_by, r.created_time, r.last_modified_by, r.last_modified_time, r.valid
        FROM t_resource r
        INNER JOIN t_role_resource rr ON r.id = rr.resource_id
        INNER JOIN t_user_role ur ON rr.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND r.valid = 1 AND rr.valid = 1 AND ur.valid = 1
          AND r.status = 1
          AND r.resource_type = 3
        ORDER BY r.order_num ASC, r.created_time ASC
    </select>

    <!-- 查询所有启用的资源 -->
    <select id="selectEnabledResources" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_resource
        WHERE status = 1 AND valid = 1
        ORDER BY order_num ASC, created_time ASC
    </select>

    <!-- 检查资源是否有子资源 -->
    <select id="hasChildren" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM t_resource
        WHERE parent_id = #{resourceId} AND valid = 1
    </select>

    <!-- 根据角色ID查询角色拥有的资源 -->
    <select id="selectByRoleIdList" resultType="com.yi.controller.user.model.ResourceVO">
        SELECT res.id,
        res.parent_id     parentId,
        res.resource_code resourceCode,
        res.resource_name resourceName,
        res.resource_type resourceType,
        res.order_num     orderNum
        FROM t_sys_resource res
        WHERE res.id in
        <foreach collection="roleIdList" item="roleId" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
        AND res.valid = 1
        AND res.status = 1
        ORDER BY res.parent_id ASC, res.order_num ASC
    </select>
</mapper>
