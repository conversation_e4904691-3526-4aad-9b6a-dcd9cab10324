<template>
  <div class="login-container">
    <div class="login-form">
      <!-- 品牌标识 -->
      <div class="login-brand">
        易炬科技
      </div>
      
      <!-- 登录标题 -->
      <h2 class="login-title">登录</h2>
      
      <!-- 登录表单 -->
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入账号"
            size="large"
            prefix-icon="User"
          >
            <template #prepend>*账号</template>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
          >
            <template #prepend>*密码</template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="loading"
            @click="handleLogin"
          >
            确认登录
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 演示提示 -->
      <div class="demo-tip">
        <el-alert
          title="演示账号"
          description="账号：admin，密码：admin"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { login } from '@/api/auth'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const loginFormRef = ref()
    const loading = ref(false)
    
    // 表单数据
    const loginForm = reactive({
      username: '',
      password: ''
    })
    
    // 表单验证规则
    const loginRules = {
      username: [
        { required: true, message: '请输入账号', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
      ]
    }
    
    // 登录处理
    const handleLogin = async () => {
      try {
        // 表单验证
        await loginFormRef.value.validate()

        loading.value = true

        // 调用真实的登录API
        const response = await login({
          username: loginForm.username,
          password: loginForm.password
        })

        if (response.code === 200) {
          // 从响应头获取token
          const token = response.data.tokenModel?.token
          if (token) {
            // 保存token和用户信息
            localStorage.setItem('token', token)
            localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo || {}))

            ElMessage.success('登录成功')

            // 跳转到首页
            router.push('/dashboard')
          } else {
            ElMessage.error('登录失败：未获取到有效token')
          }
        } else {
          ElMessage.error(response.message || '登录失败')
        }

      } catch (error) {
        console.error('登录错误:', error)

        // 如果API调用失败，回退到模拟登录
        if (loginForm.username === 'admin' && loginForm.password === 'admin') {
          const mockResponse = {
            token: 'mock-jwt-token-' + Date.now(),
            userInfo: {
              id: 1,
              name: '管理员',
              username: 'admin',
              role: 'admin'
            }
          }

          localStorage.setItem('token', mockResponse.token)
          localStorage.setItem('userInfo', JSON.stringify(mockResponse.userInfo))

          ElMessage.success('登录成功（模拟模式）')
          router.push('/dashboard')
        } else {
          ElMessage.error('登录失败，请检查账号密码或网络连接')
        }
      } finally {
        loading.value = false
      }
    }
    
    return {
      loginFormRef,
      loginForm,
      loginRules,
      loading,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
}

.login-form {
  background: #fff;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 400px;
}

.login-brand {
  text-align: center;
  margin-bottom: 40px;
  color: #409eff;
  font-size: 28px;
  font-weight: bold;
}

.login-title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

:deep(.el-input-group__prepend) {
  background-color: #f5f7fa;
  color: #606266;
  border-color: #dcdfe6;
  min-width: 60px;
  text-align: center;
}

.demo-tip {
  margin-top: 20px;
}
</style>
