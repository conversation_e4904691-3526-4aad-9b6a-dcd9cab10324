package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yi.entity.TRoleResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色资源关联表 Mapper 接口
 */
@Mapper
public interface TRoleResourceMapper extends BaseMapper<TRoleResource> {

    /**
     * 根据角色ID删除角色资源关联
     *
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据资源ID删除角色资源关联
     *
     * @param resourceId 资源ID
     * @return 删除数量
     */
    int deleteByResourceId(@Param("resourceId") Long resourceId);

    /**
     * 批量插入角色资源关联
     *
     * @param roleResources 角色资源关联列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<TRoleResource> roleResources);

    /**
     * 根据角色ID查询资源ID列表
     *
     * @param roleId 角色ID
     * @return 资源ID列表
     */
    List<Long> selectResourceIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据资源ID查询角色ID列表
     *
     * @param resourceId 资源ID
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByResourceId(@Param("resourceId") Long resourceId);

    /**
     * 根据角色ID集合查询资源ID列表
     *
     * @param roleIdList 角色ID集合
     * @return 资源ID列表
     */
    List<Long> selectResourceIdsByRoleIdList(@Param("roleId") List<Long> roleIdList);

}
