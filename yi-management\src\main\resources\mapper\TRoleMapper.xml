<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TRole">
        <id column="id" property="id" />
        <result column="role_code" property="roleCode" />
        <result column="role_name" property="roleName" />
        <result column="role_desc" property="roleDesc" />
        <result column="role_sort" property="roleSort" />
        <result column="data_scope" property="dataScope" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_code, role_name, role_desc, role_sort, data_scope, status, 
        created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <!-- 分页查询角色列表 -->
    <select id="selectRolePage" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_role
        WHERE valid = 1
        <if test="roleCode != null and roleCode != ''">
            AND role_code LIKE CONCAT('%', #{roleCode}, '%')
        </if>
        <if test="roleName != null and roleName != ''">
            AND role_name LIKE CONCAT('%', #{roleName}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY role_sort ASC, created_time DESC
    </select>

    <!-- 根据角色编码查询角色 -->
    <select id="selectByRoleCode" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_role
        WHERE role_code = #{roleCode} AND valid = 1
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
            r.id, r.role_code, r.role_name, r.role_desc, r.role_sort, r.data_scope, r.status,
            r.created_by, r.created_time, r.last_modified_by, r.last_modified_time, r.valid
        FROM t_role r
        INNER JOIN t_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId} AND r.valid = 1 AND ur.valid = 1
        ORDER BY r.role_sort ASC
    </select>

    <!-- 查询角色的资源ID列表 -->
    <select id="selectResourceIdsByRoleId" resultType="java.lang.Long">
        SELECT rr.resource_id
        FROM t_role_resource rr
        WHERE rr.role_id = #{roleId} AND rr.valid = 1
    </select>

    <!-- 查询所有启用的角色 -->
    <select id="selectEnabledRoles" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM t_role
        WHERE status = 1 AND valid = 1
        ORDER BY role_sort ASC, created_time DESC
    </select>

</mapper>
