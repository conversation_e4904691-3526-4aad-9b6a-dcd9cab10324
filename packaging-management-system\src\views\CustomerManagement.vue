<template>
  <div class="customer-management-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>客户管理</h2>
      <div class="header-actions">
        <el-button @click="handleRefresh" :icon="Refresh">刷新本页</el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="公司名称">
          <el-input
            v-model="searchForm.companyName"
            placeholder="请输入公司名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="启用中" value="1" />
            <el-option label="禁用中" value="0" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="客户类型">
          <el-select v-model="searchForm.customerType" placeholder="全部" clearable style="width: 140px">
            <el-option label="全部" value="" />
            <el-option label="合约客户" value="合约客户" />
            <el-option label="非合约客户" value="非合约客户" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="业务模式">
          <el-select v-model="searchForm.businessMode" placeholder="全部" clearable style="width: 140px">
            <el-option label="全部" value="" />
            <el-option label="租赁+流转" value="租赁+流转" />
            <el-option label="一口价" value="一口价" />
            <el-option label="静态租赁" value="静态租赁" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">查询</el-button>
          <el-button @click="handleReset" :icon="RefreshLeft">清空</el-button>
          <el-button type="primary" @click="handleExport" :icon="Download">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus">添加客户</el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        stripe
        border
      >
        <el-table-column type="index" label="#" width="60" align="center" />
        
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.enabled === 1 ? 'success' : 'danger'">
              {{ row.enabled === 1 ? '启用中' : '禁用中' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="customerType" label="客户类型" width="120" align="center" />
        <el-table-column prop="businessMode" label="业务模式" width="120" align="center" />
        <el-table-column prop="companyName" label="公司名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="registeredAddress" label="注册地址" min-width="200" show-overflow-tooltip />
        <el-table-column prop="contactPerson" label="联系人" width="100" align="center" />
        <el-table-column prop="contactPhone" label="联系方式" width="130" align="center" />
        <el-table-column prop="creator" label="创建人" width="100" align="center" />
        <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)" :icon="View">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)" :icon="Edit">编辑</el-button>
            <el-button
              :type="row.enabled === 1 ? 'danger' : 'success'"
              size="small"
              @click="handleStatusChange(row)"
            >
              {{ row.enabled === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <div class="pagination-info">
        <span>共 {{ pagination.total }} 条</span>
        <el-select v-model="pagination.pageSize" @change="handlePageSizeChange" style="width: 100px; margin-left: 10px">
          <el-option label="10条/页" :value="10" />
          <el-option label="20条/页" :value="20" />
          <el-option label="30条/页" :value="30" />
          <el-option label="50条/页" :value="50" />
        </el-select>
      </div>
      
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 30, 50]"
        layout="prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  RefreshLeft,
  Download,
  Plus,
  View,
  Edit,
  Refresh
} from '@element-plus/icons-vue'
import { customerApi } from '@/api/customer'

export default {
  name: 'CustomerManagement',
  setup() {
    const loading = ref(false)
    
    // 搜索表单
    const searchForm = reactive({
      companyName: '',
      status: '',
      customerType: '',
      businessMode: ''
    })
    
    // 表格数据
    const tableData = ref([])
    
    // 分页数据
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0
    })
    
    // 模拟数据
    const mockData = [
      {
        id: 1,
        companyName: 'GC住宅公司A类光伏设备公司',
        customerType: '合约客户',
        businessMode: '租赁+流转',
        registeredAddress: '上海市松江区松江新城',
        contactPerson: '张三',
        contactPhone: '***********',
        enabled: 0,
        creator: '管理员',
        createTime: '2024-01-15 10:30:00'
      },
      {
        id: 2,
        companyName: '易炬科技有限公司',
        customerType: '合约客户',
        businessMode: '一口价',
        registeredAddress: '北京市朝阳区建国路',
        contactPerson: '李四',
        contactPhone: '***********',
        enabled: 1,
        creator: '管理员',
        createTime: '2024-01-16 14:20:00'
      }
    ]

    // 加载客户列表
    const loadCustomerList = async () => {
      loading.value = true
      try {
        const response = await customerApi.getCustomerPage({
          ...searchForm,
          current: pagination.current,
          pageSize: pagination.pageSize
        })

        if (response.code === 200) {
          tableData.value = response.data.records || []
          pagination.total = response.data.total || 0
        } else {
          ElMessage.error(response.message || '加载数据失败')
        }

      } catch (error) {
        // 如果API调用失败，使用模拟数据
        console.warn('API调用失败，使用模拟数据:', error)

        // 模拟分页数据
        const start = (pagination.current - 1) * pagination.pageSize
        const end = start + pagination.pageSize
        tableData.value = mockData.slice(start, end)
        pagination.total = mockData.length

      } finally {
        loading.value = false
      }
    }

    // 搜索
    const handleSearch = () => {
      pagination.current = 1
      loadCustomerList()
    }

    // 重置搜索
    const handleReset = () => {
      Object.assign(searchForm, {
        companyName: '',
        status: '',
        customerType: '',
        businessMode: ''
      })
      pagination.current = 1
      loadCustomerList()
    }

    // 刷新
    const handleRefresh = () => {
      loadCustomerList()
    }

    // 导出
    const handleExport = async () => {
      try {
        ElMessage.info('正在导出，请稍候...')

        const response = await customerApi.exportCustomerList(searchForm)

        // 创建下载链接
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `客户列表_${new Date().toISOString().slice(0, 10)}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        ElMessage.success('导出成功')
      } catch (error) {
        ElMessage.error('导出失败')
        console.error('导出失败:', error)
      }
    }

    // 新增客户
    const handleAdd = () => {
      // 这里应该打开新增弹窗
      ElMessage.info('新增客户功能开发中...')
    }

    // 编辑客户
    const handleEdit = (row) => {
      // 这里应该打开编辑弹窗
      ElMessage.info(`编辑客户: ${row.companyName}`)
    }

    // 查看详情
    const handleView = (row) => {
      // 这里应该打开详情弹窗
      ElMessage.info(`查看客户详情: ${row.companyName}`)
    }

    // 状态切换
    const handleStatusChange = async (row) => {
      const action = row.enabled === 1 ? '禁用' : '启用'
      const newStatus = row.enabled === 1 ? 0 : 1

      try {
        await ElMessageBox.confirm(
          `确定要${action}客户"${row.companyName}"吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        const response = await customerApi.updateCustomerStatus(row.id, newStatus)

        if (response.code === 200) {
          row.enabled = newStatus
          ElMessage.success(`${action}成功`)
        } else {
          ElMessage.error(response.message || `${action}失败`)
        }

      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error(`${action}失败`)
          console.error('状态切换失败:', error)
        }
      }
    }

    // 分页变化
    const handlePageChange = (page) => {
      pagination.current = page
      loadCustomerList()
    }

    // 每页条数变化
    const handlePageSizeChange = (size) => {
      pagination.pageSize = size
      pagination.current = 1
      loadCustomerList()
    }

    // 初始化
    onMounted(() => {
      loadCustomerList()
    })

    return {
      loading,
      searchForm,
      tableData,
      pagination,
      loadCustomerList,
      handleSearch,
      handleReset,
      handleRefresh,
      handleExport,
      handleAdd,
      handleEdit,
      handleView,
      handleStatusChange,
      handlePageChange,
      handlePageSizeChange,
      // 图标
      Search,
      RefreshLeft,
      Download,
      Plus,
      View,
      Edit,
      Refresh
    }
  }
}
</script>

<style scoped>
.customer-management-container {
  height: calc(100vh - 60px);
  background: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: #fff;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.action-section {
  background: #fff;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.table-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination-section {
  background: #fff;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-info {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #333;
  font-weight: 500;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 按钮样式优化 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-form .el-form-item {
    margin-right: 15px;
  }

  .search-form .el-input,
  .search-form .el-select {
    width: 150px !important;
  }
}

@media (max-width: 768px) {
  .customer-management-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .pagination-section {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
}
</style>
