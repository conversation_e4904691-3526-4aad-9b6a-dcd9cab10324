<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.InventoryTransactionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.InventoryTransaction">
        <id column="id" property="id" />
        <result column="transaction_type" property="transactionType" />
        <result column="order_id" property="orderId" />
        <result column="order_no" property="orderNo" />
        <result column="detail_id" property="detailId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="first_category" property="firstCategory" />
        <result column="second_category" property="secondCategory" />
        <result column="quantity_before" property="quantityBefore" />
        <result column="quantity_change" property="quantityChange" />
        <result column="quantity_after" property="quantityAfter" />
        <result column="transaction_time" property="transactionTime" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, transaction_type, order_id, order_no, detail_id, warehouse_id, warehouse_name,
        first_category, second_category, quantity_before, quantity_change, quantity_after,
        transaction_time, created_by, created_time, remark
    </sql>

    <!-- 分页查询库存流水列表 -->
    <select id="selectTransactionPage" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inventory_transaction
        WHERE 1=1
        <if test="transactionType != null">
            AND transaction_type = #{transactionType}
        </if>
        <if test="orderId != null">
            AND order_id = #{orderId}
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="firstCategory != null">
            AND first_category = #{firstCategory}
        </if>
        <if test="startTime != null">
            AND transaction_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND transaction_time &lt;= #{endTime}
        </if>
        ORDER BY transaction_time DESC
    </select>

    <!-- 根据订单ID查询流水列表 -->
    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inventory_transaction
        WHERE order_id = #{orderId}
        ORDER BY transaction_time DESC
    </select>

    <!-- 根据订单号查询流水列表 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inventory_transaction
        WHERE order_no = #{orderNo}
        ORDER BY transaction_time DESC
    </select>

    <!-- 根据明细ID查询流水列表 -->
    <select id="selectByDetailId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM t_inventory_transaction
        WHERE detail_id = #{detailId}
        ORDER BY transaction_time DESC
    </select>

    <!-- 查询仓库库存汇总 -->
    <select id="selectInventorySummary" resultType="java.util.Map">
        SELECT 
            warehouse_id,
            warehouse_name,
            first_category,
            second_category,
            SUM(CASE WHEN transaction_type = 1 THEN quantity_change ELSE 0 END) as total_inbound,
            SUM(CASE WHEN transaction_type = 2 THEN ABS(quantity_change) ELSE 0 END) as total_outbound,
            SUM(quantity_change) as current_stock,
            COUNT(*) as transaction_count,
            MAX(transaction_time) as last_transaction_time
        FROM t_inventory_transaction
        WHERE 1=1
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="firstCategory != null">
            AND first_category = #{firstCategory}
        </if>
        <if test="secondCategory != null and secondCategory != ''">
            AND second_category = #{secondCategory}
        </if>
        GROUP BY warehouse_id, warehouse_name, first_category, second_category
    </select>

    <!-- 查询所有仓库库存汇总 -->
    <select id="selectAllInventorySummary" resultType="java.util.Map">
        SELECT 
            warehouse_id,
            warehouse_name,
            first_category,
            second_category,
            SUM(CASE WHEN transaction_type = 1 THEN quantity_change ELSE 0 END) as total_inbound,
            SUM(CASE WHEN transaction_type = 2 THEN ABS(quantity_change) ELSE 0 END) as total_outbound,
            SUM(quantity_change) as current_stock,
            COUNT(*) as transaction_count,
            MAX(transaction_time) as last_transaction_time
        FROM t_inventory_transaction
        GROUP BY warehouse_id, warehouse_name, first_category, second_category
        ORDER BY warehouse_id, first_category
    </select>

    <!-- 查询库存变化趋势 -->
    <select id="selectInventoryTrend" resultType="java.util.Map">
        SELECT 
            DATE(transaction_time) as transaction_date,
            SUM(CASE WHEN transaction_type = 1 THEN quantity_change ELSE 0 END) as daily_inbound,
            SUM(CASE WHEN transaction_type = 2 THEN ABS(quantity_change) ELSE 0 END) as daily_outbound,
            SUM(quantity_change) as daily_net_change,
            COUNT(*) as daily_transaction_count
        FROM t_inventory_transaction
        WHERE 1=1
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="firstCategory != null">
            AND first_category = #{firstCategory}
        </if>
        <if test="secondCategory != null and secondCategory != ''">
            AND second_category = #{secondCategory}
        </if>
        <if test="startTime != null">
            AND transaction_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND transaction_time &lt;= #{endTime}
        </if>
        GROUP BY DATE(transaction_time)
        ORDER BY transaction_date
    </select>

    <!-- 查询当前库存数量 -->
    <select id="selectCurrentStock" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(quantity_change), 0)
        FROM t_inventory_transaction
        WHERE 1=1
        <if test="warehouseId != null">
            AND warehouse_id = #{warehouseId}
        </if>
        <if test="firstCategory != null">
            AND first_category = #{firstCategory}
        </if>
        <if test="secondCategory != null and secondCategory != ''">
            AND second_category = #{secondCategory}
        </if>
    </select>

    <!-- 批量插入库存流水 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_inventory_transaction (
            transaction_type, order_id, order_no, detail_id, warehouse_id, warehouse_name,
            first_category, second_category, quantity_before, quantity_change, quantity_after,
            transaction_time, created_by, created_time, remark
        ) VALUES
        <foreach collection="transactions" item="transaction" separator=",">
            (
                #{transaction.transactionType}, #{transaction.orderId}, #{transaction.orderNo}, #{transaction.detailId},
                #{transaction.warehouseId}, #{transaction.warehouseName}, #{transaction.firstCategory}, #{transaction.secondCategory},
                #{transaction.quantityBefore}, #{transaction.quantityChange}, #{transaction.quantityAfter},
                #{transaction.transactionTime}, #{transaction.createdBy}, #{transaction.createdTime}, #{transaction.remark}
            )
        </foreach>
    </insert>

    <!-- 统计交易类型数量 -->
    <select id="selectTransactionTypeStatistics" resultType="java.util.Map">
        SELECT 
            transaction_type,
            CASE 
                WHEN transaction_type = 1 THEN '入库'
                WHEN transaction_type = 2 THEN '出库'
                WHEN transaction_type = 3 THEN '调拨'
                WHEN transaction_type = 4 THEN '盘点'
                ELSE '未知'
            END as transaction_type_name,
            COUNT(*) as transaction_count,
            SUM(ABS(quantity_change)) as total_quantity,
            AVG(ABS(quantity_change)) as avg_quantity
        FROM t_inventory_transaction
        WHERE 1=1
        <if test="startTime != null">
            AND transaction_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND transaction_time &lt;= #{endTime}
        </if>
        GROUP BY transaction_type
        ORDER BY transaction_type
    </select>

    <!-- 统计仓库交易数量 -->
    <select id="selectWarehouseStatistics" resultType="java.util.Map">
        SELECT 
            warehouse_id,
            warehouse_name,
            COUNT(*) as transaction_count,
            SUM(CASE WHEN transaction_type = 1 THEN quantity_change ELSE 0 END) as total_inbound,
            SUM(CASE WHEN transaction_type = 2 THEN ABS(quantity_change) ELSE 0 END) as total_outbound,
            SUM(quantity_change) as net_change,
            MAX(transaction_time) as last_transaction_time
        FROM t_inventory_transaction
        WHERE 1=1
        <if test="startTime != null">
            AND transaction_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND transaction_time &lt;= #{endTime}
        </if>
        GROUP BY warehouse_id, warehouse_name
        ORDER BY warehouse_id
    </select>

</mapper>
