package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 资源表（菜单/按钮/接口权限）
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_resource")
@ApiModel(value = "TResource对象", description = "资源表")
public class TResource extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 父资源ID
     */
    @ApiModelProperty(value = "父资源ID")
    @TableField("parent_id")
    private Long parentId;

    /**
     * 资源编码
     */
    @ApiModelProperty(value = "资源编码")
    @TableField("resource_code")
    private String resourceCode;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    @TableField("resource_name")
    private String resourceName;

    /**
     * 资源类型：1-目录，2-菜单，3-按钮，4-接口
     */
    @ApiModelProperty(value = "资源类型：1-目录，2-菜单，3-按钮，4-接口")
    @TableField("resource_type")
    private Integer resourceType;

    /**
     * 路由地址
     */
    @ApiModelProperty(value = "路由地址")
    @TableField("path")
    private String path;

    /**
     * 组件路径
     */
    @ApiModelProperty(value = "组件路径")
    @TableField("component")
    private String component;

    /**
     * 权限标识
     */
    @ApiModelProperty(value = "权限标识")
    @TableField("perms")
    private String perms;

    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    @TableField("icon")
    private String icon;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    @TableField("order_num")
    private Integer orderNum;

    /**
     * 是否显示：0-隐藏，1-显示
     */
    @ApiModelProperty(value = "是否显示：0-隐藏，1-显示")
    @TableField("visible")
    private Integer visible;

    /**
     * 状态：0-禁用，1-启用
     */
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    @TableField("status")
    private Integer status;

    private List<TResource> subResourceList;
}
