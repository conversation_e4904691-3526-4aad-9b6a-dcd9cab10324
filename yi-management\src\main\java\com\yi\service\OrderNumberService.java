package com.yi.service;

import com.yi.entity.OrderSequence;
import com.yi.mapper.OrderSequenceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 单号生成服务类
 */
@Service
public class OrderNumberService {

    @Autowired
    private OrderSequenceMapper orderSequenceMapper;

    private static final String ORDER_PREFIX = "F";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 生成出库单号
     */
    @Transactional(rollbackFor = Exception.class)
    public String generateOutboundOrderNo() {
        return generateOrderNoByDate(getCurrentDateKey());
    }

    /**
     * 生成入库单号（与出库单号一致）
     */
    @Transactional(rollbackFor = Exception.class)
    public String generateInboundOrderNo() {
        return generateOrderNoByDate(getCurrentDateKey());
    }

    /**
     * 根据指定日期生成单号
     */
    @Transactional(rollbackFor = Exception.class)
    public String generateOrderNoByDate(String dateKey) {
        // 确保当日序列记录存在
        initSequenceIfNotExists(dateKey);

        // 获取当前序列号
        OrderSequence sequence = orderSequenceMapper.selectByDateKey(dateKey);
        if (sequence == null) {
            throw new RuntimeException("获取序列号失败");
        }

        // 递增序列号
        Integer newSequenceValue = sequence.getSequenceValue() + 1;

        // 更新序列号
        int updateResult = orderSequenceMapper.updateSequence(dateKey, newSequenceValue);
        if (updateResult <= 0) {
            throw new RuntimeException("更新序列号失败");
        }

        // 生成单号：F + 年月日 + 4位序列号
        return ORDER_PREFIX + dateKey + String.format("%04d", newSequenceValue);
    }

    /**
     * 获取当日已生成的单号数量
     */
    public Integer getTodayOrderCount() {
        return getOrderCountByDate(getCurrentDateKey());
    }

    /**
     * 获取指定日期已生成的单号数量
     */
    public Integer getOrderCountByDate(String dateKey) {
        OrderSequence sequence = orderSequenceMapper.selectByDateKey(dateKey);
        return sequence != null ? sequence.getSequenceValue() : 0;
    }

    /**
     * 获取当前日期键
     */
    private String getCurrentDateKey() {
        return LocalDateTime.now().format(DATE_FORMATTER);
    }

    /**
     * 初始化序列记录（如果不存在）
     */
    private void initSequenceIfNotExists(String dateKey) {
        OrderSequence existing = orderSequenceMapper.selectByDateKey(dateKey);
        if (existing == null) {
            orderSequenceMapper.initTodaySequence(dateKey);
        }
    }
}
