<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="200px" class="layout-sidebar">
        <div class="sidebar-header">
          <h3>管理工作台</h3>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          background-color="#545c64"
          text-color="#fff"
          active-text-color="#ffd04b"
          router
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <span>工作台</span>
          </el-menu-item>
          
          <el-sub-menu index="order">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>订单管理</span>
            </template>
            <el-menu-item index="/order/list">订单列表</el-menu-item>
            <el-menu-item index="/order/add">新增订单</el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="warehouse">
            <template #title>
              <el-icon><Box /></el-icon>
              <span>仓库管理</span>
            </template>
            <el-menu-item index="/warehouse/list">仓库列表</el-menu-item>
            <el-menu-item index="/warehouse/stock">库存管理</el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="customer">
            <template #title>
              <el-icon><User /></el-icon>
              <span>客户管理</span>
            </template>
            <el-menu-item index="/dashboard/customer-management">客户列表</el-menu-item>
            <el-menu-item index="/customer/add">新增客户</el-menu-item>
          </el-sub-menu>
          
          <el-sub-menu index="system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/system/user">用户管理</el-menu-item>
            <el-menu-item index="/system/role">角色管理</el-menu-item>
            <el-menu-item index="/system/menu">菜单管理</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>
      
      <!-- 主要内容区域 -->
      <el-container>
        <!-- 顶部导航栏 -->
        <el-header class="layout-header">
          <div class="header-left">
            <h2>易炬科技</h2>
          </div>
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-icon><Avatar /></el-icon>
                {{ userInfo.name || '用户' }}
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容区域 -->
        <el-main class="layout-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 修改密码弹窗 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="密码修改"
      width="500px"
      :before-close="handleClosePasswordDialog"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="120px"
      >
        <el-form-item label="*原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
            placeholder="请输入原密码"
          />
        </el-form-item>
        <el-form-item label="*新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        <el-form-item label="*再次确认新密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            placeholder="请再次确认新密码"
          />
        </el-form-item>
        <el-form-item>
          <div class="password-tip">
            提示：新密码必须包含 大写字母，小写字母，数字，且位数在8-20位之间
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClosePasswordDialog">取消</el-button>
          <el-button type="primary" @click="handleChangePassword">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { changePassword, logout } from '../api/auth'
import { validatePassword } from '../utils'

export default {
  name: 'Layout',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const passwordFormRef = ref()
    const passwordDialogVisible = ref(false)
    
    // 用户信息
    const userInfo = reactive({
      name: '用户'
    })
    
    // 当前激活的菜单
    const activeMenu = computed(() => route.path)
    
    // 修改密码表单
    const passwordForm = reactive({
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    // 密码验证规则
    const passwordRules = {
      oldPassword: [
        { required: true, message: '请输入原密码', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { 
          validator: (rule, value, callback) => {
            if (!validatePassword(value)) {
              callback(new Error('密码格式错误'))
            } else {
              callback()
            }
          }, 
          trigger: 'blur' 
        }
      ],
      confirmPassword: [
        { required: true, message: '请再次确认新密码', trigger: 'blur' },
        { 
          validator: (rule, value, callback) => {
            if (value !== passwordForm.newPassword) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          }, 
          trigger: 'blur' 
        }
      ]
    }
    
    // 处理下拉菜单命令
    const handleCommand = (command) => {
      switch (command) {
        case 'changePassword':
          passwordDialogVisible.value = true
          break
        case 'logout':
          handleLogout()
          break
      }
    }
    
    // 修改密码
    const handleChangePassword = async () => {
      try {
        await passwordFormRef.value.validate()
        
        await changePassword({
          oldPassword: passwordForm.oldPassword,
          newPassword: passwordForm.newPassword
        })
        
        ElMessage.success('修改成功')
        passwordDialogVisible.value = false
        resetPasswordForm()
        
      } catch (error) {
        ElMessage.error(error.message || '修改失败')
      }
    }
    
    // 关闭密码弹窗
    const handleClosePasswordDialog = () => {
      passwordDialogVisible.value = false
      resetPasswordForm()
    }
    
    // 重置密码表单
    const resetPasswordForm = () => {
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
      passwordFormRef.value?.clearValidate()
    }
    
    // 退出登录
    const handleLogout = async () => {
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await logout()
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        
        ElMessage.success('退出成功')
        router.push('/login')
        
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('退出失败')
        }
      }
    }
    
    // 初始化用户信息
    onMounted(() => {
      const savedUserInfo = localStorage.getItem('userInfo')
      if (savedUserInfo) {
        Object.assign(userInfo, JSON.parse(savedUserInfo))
      }
    })
    
    return {
      activeMenu,
      userInfo,
      passwordDialogVisible,
      passwordFormRef,
      passwordForm,
      passwordRules,
      handleCommand,
      handleChangePassword,
      handleClosePasswordDialog
    }
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.layout-sidebar {
  background: #545c64;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 200px;
  z-index: 1000;
  overflow-y: auto;
}

.sidebar-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #434a50;
}

.sidebar-header h3 {
  color: #fff;
  margin: 0;
  font-size: 16px;
}

.sidebar-menu {
  border-right: none;
  height: calc(100vh - 80px);
}

.layout-header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 200px;
  height: 60px;
  width: calc(100vw - 200px);
  position: fixed;
  top: 0;
  z-index: 999;
}

.header-left h2 {
  margin: 0;
  color: #409eff;
  font-size: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #606266;
}

.user-info .el-icon {
  margin: 0 5px;
}

.layout-main {
  margin-left: 200px;
  margin-top: 60px;
  padding: 0;
  background: #f0f2f5;
  overflow-y: auto;
  height: calc(100vh - 60px);
  width: calc(100vw - 200px);
}

.password-tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}
</style>
