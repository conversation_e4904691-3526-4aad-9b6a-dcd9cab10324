package com.yi.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 库存流水表
 */
@Data
@TableName("t_inventory_transaction")
@ApiModel(value = "InventoryTransaction对象", description = "库存流水表")
public class InventoryTransaction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 交易类型：1-入库，2-出库，3-调拨，4-盘点
     */
    @ApiModelProperty(value = "交易类型：1-入库，2-出库，3-调拨，4-盘点")
    @TableField("transaction_type")
    private Integer transactionType;

    /**
     * 关联订单ID
     */
    @ApiModelProperty(value = "关联订单ID")
    @TableField("order_id")
    private Long orderId;

    /**
     * 关联订单号
     */
    @ApiModelProperty(value = "关联订单号")
    @TableField("order_no")
    private String orderNo;

    /**
     * 关联明细ID
     */
    @ApiModelProperty(value = "关联明细ID")
    @TableField("detail_id")
    private Long detailId;

    /**
     * 仓库ID
     */
    @ApiModelProperty(value = "仓库ID")
    @TableField("warehouse_id")
    private Long warehouseId;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 一级类目
     */
    @ApiModelProperty(value = "一级类目")
    @TableField("first_category")
    private Integer firstCategory;

    /**
     * 二级类目
     */
    @ApiModelProperty(value = "二级类目")
    @TableField("second_category")
    private String secondCategory;

    /**
     * 变更前数量
     */
    @ApiModelProperty(value = "变更前数量")
    @TableField("quantity_before")
    private Integer quantityBefore;

    /**
     * 变更数量（正数为增加，负数为减少）
     */
    @ApiModelProperty(value = "变更数量")
    @TableField("quantity_change")
    private Integer quantityChange;

    /**
     * 变更后数量
     */
    @ApiModelProperty(value = "变更后数量")
    @TableField("quantity_after")
    private Integer quantityAfter;

    /**
     * 交易时间
     */
    @ApiModelProperty(value = "交易时间")
    @TableField("transaction_time")
    private LocalDateTime transactionTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
}
