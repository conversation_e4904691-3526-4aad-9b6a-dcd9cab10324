import request from '@/utils/request'

// 登录
export function login(data) {
  return request({
    url: '/api/management/login',
    method: 'post',
    data: {
      userAccount: data.username,
      password: data.password
    }
  })
}

// 退出登录
export function logout() {
  return request({
    url: '/api/management/logout',
    method: 'post'
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/auth/change-password',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/auth/user-info',
    method: 'get'
  })
}
