package com.yi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.InboundOrder;
import com.yi.entity.OutboundOrderDetail;
import com.yi.mapper.OutboundOrderDetailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 出库单明细表 服务类
 */
@Service
public class OutboundOrderDetailService extends ServiceImpl<OutboundOrderDetailMapper, OutboundOrderDetail> {

    @Autowired
    private InboundOrderService inboundOrderService;

    /**
     * 分页查询出库单明细列表
     */
    public IPage<OutboundOrderDetail> selectDetailPage(Page<OutboundOrderDetail> page, Long outboundOrderId,
                                                       Long sourceInboundOrderId, String sourceInboundOrderNo) {
        return baseMapper.selectDetailPage(page, outboundOrderId, sourceInboundOrderId, sourceInboundOrderNo);
    }

    /**
     * 根据出库单ID查询明细列表
     */
    public List<OutboundOrderDetail> selectByOutboundOrderId(Long outboundOrderId) {
        return baseMapper.selectByOutboundOrderId(outboundOrderId);
    }

    /**
     * 根据来源入库单ID查询明细列表
     */
    public List<OutboundOrderDetail> selectBySourceInboundOrderId(Long sourceInboundOrderId) {
        return baseMapper.selectBySourceInboundOrderId(sourceInboundOrderId);
    }

    /**
     * 根据来源入库单号查询明细列表
     */
    public List<OutboundOrderDetail> selectBySourceInboundOrderNo(String sourceInboundOrderNo) {
        return baseMapper.selectBySourceInboundOrderNo(sourceInboundOrderNo);
    }

    /**
     * 查询出库单明细汇总信息
     */
    public Map<String, Object> getDetailSummary(Long outboundOrderId) {
        return baseMapper.selectDetailSummary(outboundOrderId);
    }

    /**
     * 创建出库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createDetail(OutboundOrderDetail detail) {
        // 设置默认值
        if (detail.getActualQuantity() == null) {
            detail.setActualQuantity(0);
        }
        if (detail.getRollbackQuantity() == null) {
            detail.setRollbackQuantity(0);
        }
        detail.setValid(1);
        detail.setCreatedTime(LocalDateTime.now());
        detail.setLastModifiedTime(LocalDateTime.now());

        return save(detail);
    }

    /**
     * 批量创建出库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateDetails(List<OutboundOrderDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return true;
        }

        // 设置默认值
        LocalDateTime now = LocalDateTime.now();
        for (OutboundOrderDetail detail : details) {
            if (detail.getActualQuantity() == null) {
                detail.setActualQuantity(0);
            }
            if (detail.getRollbackQuantity() == null) {
                detail.setRollbackQuantity(0);
            }
            detail.setValid(1);
            detail.setCreatedTime(now);
            detail.setLastModifiedTime(now);
        }

        return baseMapper.batchInsert(details) > 0;
    }

    /**
     * 更新出库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDetail(OutboundOrderDetail detail) {
        detail.setLastModifiedTime(LocalDateTime.now());
        return updateById(detail);
    }

    /**
     * 批量更新出库单明细的实际数量
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateActualQuantity(List<OutboundOrderDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return true;
        }

        return baseMapper.batchUpdateActualQuantity(details) > 0;
    }

    /**
     * 批量更新出库单明细的回退数量
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateRollbackQuantity(List<OutboundOrderDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            return true;
        }

        return baseMapper.batchUpdateRollbackQuantity(details) > 0;
    }

    /**
     * 删除出库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDetail(Long id) {
        OutboundOrderDetail detail = new OutboundOrderDetail();
        detail.setId(id);
        detail.setValid(0);
        detail.setLastModifiedTime(LocalDateTime.now());
        return updateById(detail);
    }

    /**
     * 根据出库单ID删除所有明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByOutboundOrderId(Long outboundOrderId, String lastModifiedBy) {
        return baseMapper.deleteByOutboundOrderId(outboundOrderId, lastModifiedBy) > 0;
    }

    /**
     * 统计出库单明细数量
     */
    public Integer countByOutboundOrderId(Long outboundOrderId) {
        return baseMapper.countByOutboundOrderId(outboundOrderId);
    }

    /**
     * 查询出库单的总计划数量
     */
    public Integer sumPlannedQuantityByOutboundOrderId(Long outboundOrderId) {
        return baseMapper.sumPlannedQuantityByOutboundOrderId(outboundOrderId);
    }

    /**
     * 查询出库单的总实际数量
     */
    public Integer sumActualQuantityByOutboundOrderId(Long outboundOrderId) {
        return baseMapper.sumActualQuantityByOutboundOrderId(outboundOrderId);
    }

    /**
     * 查询出库单的总回退数量
     */
    public Integer sumRollbackQuantityByOutboundOrderId(Long outboundOrderId) {
        return baseMapper.sumRollbackQuantityByOutboundOrderId(outboundOrderId);
    }

    /**
     * 查询来源入库单的使用情况
     */
    public Map<String, Object> getSourceUsageStatistics(Long sourceInboundOrderId) {
        return baseMapper.selectSourceUsageStatistics(sourceInboundOrderId);
    }

    /**
     * 根据入库单自动生成出库单明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean generateDetailsFromInboundOrders(Long outboundOrderId, List<Long> sourceInboundOrderIds,
                                                    List<Integer> quantities, String createdBy) {
        if (CollectionUtils.isEmpty(sourceInboundOrderIds) || CollectionUtils.isEmpty(quantities)) {
            throw new RuntimeException("来源入库单ID和数量不能为空");
        }

        if (sourceInboundOrderIds.size() != quantities.size()) {
            throw new RuntimeException("来源入库单ID和数量的数量必须一致");
        }

        // 生成明细列表
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < sourceInboundOrderIds.size(); i++) {
            Long sourceInboundOrderId = sourceInboundOrderIds.get(i);
            Integer quantity = quantities.get(i);

            // 获取来源入库单信息
            InboundOrder sourceInboundOrder = inboundOrderService.getById(sourceInboundOrderId);
            if (sourceInboundOrder == null) {
                throw new RuntimeException("来源入库单不存在：" + sourceInboundOrderId);
            }

            // 创建出库单明细
            OutboundOrderDetail detail = new OutboundOrderDetail();
            detail.setOutboundOrderId(outboundOrderId);
            detail.setPlannedQuantity(quantity);
            detail.setActualQuantity(0);
            detail.setRollbackQuantity(0);
            detail.setSourceInboundOrderId(sourceInboundOrderId);
            detail.setSourceInboundOrderNo(sourceInboundOrder.getOrderNo());
            detail.setCreatedBy(createdBy);
            detail.setCreatedTime(now);
            detail.setLastModifiedBy(createdBy);
            detail.setLastModifiedTime(now);
            detail.setValid(1);

            if (!save(detail)) {
                throw new RuntimeException("创建出库单明细失败");
            }
        }

        return true;
    }

    /**
     * 验证出库单明细的库存可用性
     */
    public Map<String, Object> validateStockAvailability(List<OutboundOrderDetail> details) {
        Map<String, Object> result = new HashMap<>();
        boolean isValid = true;
        StringBuilder errorMessage = new StringBuilder();

        if (!CollectionUtils.isEmpty(details)) {
            for (OutboundOrderDetail detail : details) {
                if (detail.getSourceInboundOrderId() != null) {
                    // 检查来源入库单的可用库存
                    InboundOrder sourceInboundOrder = inboundOrderService.getById(detail.getSourceInboundOrderId());
                    if (sourceInboundOrder == null) {
                        isValid = false;
                        errorMessage.append("来源入库单不存在：").append(detail.getSourceInboundOrderId()).append("; ");
                        continue;
                    }

                    // 检查库存是否足够
                    Integer availableQuantity = sourceInboundOrder.getActualQuantity() - 
                        (sourceInboundOrder.getActualQuantity() - sourceInboundOrder.getPlannedQuantity());
                    if (detail.getPlannedQuantity() > availableQuantity) {
                        isValid = false;
                        errorMessage.append("入库单").append(sourceInboundOrder.getOrderNo())
                                  .append("可用库存不足，需要：").append(detail.getPlannedQuantity())
                                  .append("，可用：").append(availableQuantity).append("; ");
                    }
                }
            }
        }

        result.put("isValid", isValid);
        result.put("errorMessage", errorMessage.toString());
        return result;
    }
}
