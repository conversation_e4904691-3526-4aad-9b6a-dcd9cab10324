CREATE TABLE `t_customer_company` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      customer_company_type             varchar(50)    default ''   null comment '客户类型 1合约客户 2非合约客户',
                                      `company_name` varchar(200) NOT NULL COMMENT '公司名称',
                                      `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
                                      `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
                                      `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
                                      `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
                                      `area_id` bigint(20) DEFAULT NULL COMMENT '区县ID',
                                      `area_name` varchar(50) DEFAULT NULL COMMENT '区县名称',
                                      `detailed_address` varchar(500) DEFAULT NULL COMMENT '详细地址',
                                      `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
                                      `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系方式',
                                      `invoice_type` int(4) DEFAULT NULL COMMENT '增值税开票发票资料：1-后补，2-与客户公司一致',
                                      `invoice_company_name` varchar(200) DEFAULT NULL COMMENT '开票公司名称',
                                      `tax_number` varchar(50) DEFAULT NULL COMMENT '税号',
                                      `invoice_contact_person` varchar(50) DEFAULT NULL COMMENT '发票联系人',
                                      `invoice_mobile` varchar(20) DEFAULT NULL COMMENT '发票联系手机号',
                                      `invoice_email` varchar(100) DEFAULT NULL COMMENT '发票邮箱',
                                      `bank_name` varchar(200) DEFAULT NULL COMMENT '开户银行名称',
                                      `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
                                      `enabled` int(4) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
                                      `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                      `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                                      `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                      `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                                      `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户公司表';

CREATE TABLE `t_sku` (
                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                         `first_category` int(4) NOT NULL default 0 COMMENT '一级类目 1:共享托盘',
                         `second_category` varchar(50) DEFAULT NULL COMMENT '二级类目',
                         `third_category` varchar(50) DEFAULT NULL COMMENT '三级类目',
                         `length` int(4) NOT NULL default 0 COMMENT '长度(mm)',
                         `width` int(4) NOT NULL default 0 COMMENT '宽度(mm)',
                         `height` int(4) NOT NULL default 0 COMMENT '高度(mm)',
                         `weight` decimal(10,2) DEFAULT NULL COMMENT '重量(Kg)',
                         `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                         `enabled` int(4) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
                         `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                         `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                         `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                         `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                         `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SKU表';


CREATE TABLE `t_customer_warehouse` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                               `company_id` bigint(20) NOT NULL COMMENT '公司ID',
                               `warehouse_name` varchar(200) NOT NULL COMMENT '仓库名称',
                               `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
                               `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
                               `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
                               `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
                               `area_id` bigint(20) DEFAULT NULL COMMENT '区县ID',
                               `area_name` varchar(50) DEFAULT NULL COMMENT '区县名称',
                               `detailed_address` varchar(500) DEFAULT NULL COMMENT '详细地址',
                               `contact_person` varchar(50) DEFAULT NULL COMMENT '收货人',
                               `mobile_phone` varchar(20) DEFAULT NULL COMMENT '手机号',
                               `landline_phone` varchar(20) DEFAULT NULL COMMENT '座机号',
                               `enabled` int(4) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
                               `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                               `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                               `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                               `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                               `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户仓库表';

CREATE TABLE `t_warehouse_sku` (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
                                   `first_category` int(4) NOT NULL default 0 COMMENT '一级类目 1:共享托盘',
                                   `second_category` varchar(50) DEFAULT NULL COMMENT '二级类目',
                                   `enabled` int(4) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
                                   `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                   `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                                   `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                   `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                                   `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库SKU关联表';

CREATE TABLE `t_customer_relate_downstream_address` (
                                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                        `company_id` bigint(20) NOT NULL COMMENT '公司ID',
                                                        `downstream_customer_company_id` bigint(20) NOT NULL COMMENT '下游客户ID',
                                                        `warehouse_id` bigint(20) NOT NULL COMMENT '下游客户仓库ID',
                                                        `enabled` int(4) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
                                                        `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                                        `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                        `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                                                        `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                                        `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户下游地址关联表';



-- 合同管理表
CREATE TABLE `t_contract` (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                              `contract_no` varchar(100) NOT NULL COMMENT '合同编号（唯一）',
                              `out_customer_company_id` int(4) NOT NULL COMMENT '我司主体',
                              `customer_company_id` bigint(20) NOT NULL COMMENT '客户主体ID',
                              `contract_status` int(4) NOT NULL DEFAULT '1' COMMENT '合同状态：1-待生效，2-生效中，3-已到期，4-已作废',
                              `archive_status` int(4) NOT NULL DEFAULT '1' COMMENT '归档状态：1-待归档，2-已归档',
                              `effective_date` date NOT NULL COMMENT '合同生效日期',
                              `expiry_date` date NOT NULL COMMENT '合同失效日期',
                              `cancel_reason` varchar(1000) DEFAULT NULL COMMENT '取消原因',
                              `remark` varchar(1000) DEFAULT NULL COMMENT '备注信息',
                              `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                              `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                              `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                              `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同管理表';


-- 合同附件表
CREATE TABLE `t_general_file` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `type` int(1) DEFAULT '1' COMMENT '类型: 1.销售合同, 2.发运订单, 3.供应商营业执照',
                                  `file_type` varchar(10) NOT NULL DEFAULT '' COMMENT '文件类型',
                                  `related_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '关联Id',
                                  `file_path` varchar(300) NOT NULL DEFAULT '' COMMENT '文件路径',
                                  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                                  `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                  `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用文件表';


-- 合同SKU明细表
CREATE TABLE `t_contract_sku` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `contract_id` bigint(20) NOT NULL COMMENT '合同ID',
                                  `first_category` int(4) NOT NULL default 0 COMMENT '一级类目 1:共享托盘',
                                  `business_mode` int(4) NOT NULL default 0 COMMENT '业务模式 1:静态租赁 2:租赁+流转 3:一口价',
                                  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                                  `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                  `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同SKU明细表';



CREATE TABLE `t_contract_log` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `contract_id` bigint(20) NOT NULL COMMENT '合同ID',
                                  `action` int(4) NOT NULL COMMENT '操作动作：1-编辑合同，2-作废合同',
                                  `operator` varchar(50) NOT NULL COMMENT '操作人',
                                  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
                                  `remark` varchar(1000) DEFAULT NULL COMMENT '备注信息',
                                  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                                  `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                  `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同操作日志表';




-- 发运订单表
CREATE TABLE `t_shipping_order` (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                           `order_no` varchar(50) NOT NULL COMMENT '订单号',
                           `status` int(4) NOT NULL DEFAULT 1000 COMMENT '订单状态：1000-待发货，2000-发货中，3000-已完结，4000-已取消',
                           `contract_code`  varchar(50) DEFAULT NULL COMMENT '合同编号',
                           `customer_company_id` bigint(20) NOT NULL COMMENT '客户公司ID',
                           `warehouse_id` bigint(20) NOT NULL COMMENT '收货仓库ID',
                           `first_category` int(4) NOT NULL default 0 COMMENT '一级类目 1:共享托盘',
                           `second_category` varchar(50) DEFAULT NULL COMMENT '二级类目',
                           `count` int(11) NOT NULL DEFAULT '0' COMMENT '需求数量',
                           `shipped_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '发货数量',
                           `received_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '签收数量',
                           `demand_time` date NOT NULL COMMENT '需求时间',
                           `cancel_reason` varchar(500) DEFAULT NULL COMMENT '取消原因',
                           `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                           `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                           `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                           `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                           `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发运订单表';


-- 发货需求表
CREATE TABLE `t_shipping_demand` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `order_no` varchar(50) NOT NULL COMMENT '发运订单号',
                                     `status` int(4) NOT NULL DEFAULT 1000 COMMENT '发货状态：1000-待发货，2000-已发货，3000-已完结，4000-已取消',
                                     `customer_company_id` bigint(20) NOT NULL COMMENT '客户公司ID',
                                     `warehouse_id` bigint(20) NOT NULL COMMENT '收货仓库ID',
                                     `first_category` int(4) NOT NULL default 0 COMMENT '一级类目 1:共享托盘',
                                     `second_category` varchar(50) DEFAULT NULL COMMENT '二级类目',
                                     `demand_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '需求数量',
                                     `pending_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '待确认数量',
                                     `shipped_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '发货数量',
                                     `unexecuted_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '未执行数量',
                                     `demand_time` date NOT NULL COMMENT '需求时间',
                                     `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                     `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                     `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                                     `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                     `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发货需求表';

-- 供应商表
CREATE TABLE `t_supplier` (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                              `supplier_name` varchar(200) NOT NULL COMMENT '供应商名称',
                              `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
                              `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系方式',
                              `bank_name` varchar(200) DEFAULT NULL COMMENT '开户行',
                              `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
                              `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                              `enabled` int(4) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
                              `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                              `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                              `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                              `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                              PRIMARY KEY (`id`),
                              KEY `idx_supplier_name` (`supplier_name`) COMMENT '供应商名称索引',
                              KEY `idx_enabled_valid` (`enabled`, `valid`) COMMENT '启用状态和有效性复合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商表';


CREATE TABLE `t_shipping_order_log` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `order_id` bigint(20) NOT NULL COMMENT '订单ID',
                                        `action` int(4) NOT NULL COMMENT '动作：1000-取消订单，2000-完结订单',
                                        `operator` varchar(50) NOT NULL COMMENT '操作人',
                                        `operate_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_order_id` (`order_id`),
                                        KEY `idx_operate_time` (`operate_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发运订单日志表';

-- 供应商仓库表
CREATE TABLE `t_supplier_warehouse` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
                                        `warehouse_name` varchar(200) NOT NULL COMMENT '仓库名称',
                                        `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
                                        `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
                                        `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
                                        `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
                                        `area_id` bigint(20) DEFAULT NULL COMMENT '区县ID',
                                        `area_name` varchar(50) DEFAULT NULL COMMENT '区县名称',
                                        `detailed_address` varchar(500) DEFAULT NULL COMMENT '地址详情',
                                        `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
                                        `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系方式',
                                        `enabled` int(4) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                        `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                                        `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                        `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商仓库表';

-- 云仓表
CREATE TABLE `t_cloud_warehouse` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `warehouse_name` varchar(200) NOT NULL COMMENT '仓库名称',
                                     `warehouse_type` int(4) NOT NULL COMMENT '仓库类型：1-中心仓，2-卫星仓，3-虚拟仓',
                                     `warehouse_attribute` int(4) NOT NULL COMMENT '仓库属性：1-自有，2-第三方',
                                     `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
                                     `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
                                     `city_id` bigint(20) DEFAULT NULL COMMENT '城市ID',
                                     `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
                                     `area_id` bigint(20) DEFAULT NULL COMMENT '区县ID',
                                     `area_name` varchar(50) DEFAULT NULL COMMENT '区县名称',
                                     `detailed_address` varchar(500) DEFAULT NULL COMMENT '地址详情',
                                     `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
                                     `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系方式',
                                     `working_hours` varchar(200) DEFAULT NULL COMMENT '作业时间',
                                     `enabled` int(4) NOT NULL DEFAULT '1' COMMENT '启用状态：1-启用，0-禁用',
                                     `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                     `valid` int(4) NOT NULL DEFAULT '1' COMMENT '有效性：1-有效，0-无效',
                                     `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
                                     `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `last_modified_by` varchar(50) DEFAULT NULL COMMENT '最后修改人',
                                     `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='云仓表';
