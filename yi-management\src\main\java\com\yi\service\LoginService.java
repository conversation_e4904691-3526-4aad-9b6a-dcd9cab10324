package com.yi.service;

import com.yi.configuration.exception.BizException;
import com.yi.configuration.jwt.TokenModule;
import com.yi.constant.CommonConstant;
import com.yi.controller.user.io.ManagementLoginRequestModel;
import com.yi.controller.user.io.ManagementLoginResponseModel;
import com.yi.controller.user.io.TokenModel;
import com.yi.controller.user.io.WebUiLoginUserInfoModel;
import com.yi.controller.user.model.ResourceVO;
import com.yi.entity.TUser;
import com.yi.enums.LoginExceptionCodeEnum;
import com.yi.mapper.TResourceMapper;
import com.yi.mapper.TRoleResourceMapper;
import com.yi.mapper.TUserMapper;
import com.yi.mapper.TUserRoleMapper;
import com.yi.utils.BcryptUtils;
import com.yi.utils.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LoginService {

    @Resource
    private CommonService commonService;

    @Resource
    private AuthService authService;

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private PermissionService permissionService;
    @Resource
    private TUserMapper sysUserMapper;
    @Resource
    private TUserRoleMapper sysUserRoleMapper;
    @Resource
    private TRoleResourceMapper sysRoleResourceMapper;
    @Resource
    private TResourceMapper sysResourceMapper;


    /**
     * 后台管理系统登录
     *
     * @param requestModel requestModel
     * @return result
     */
    public ManagementLoginResponseModel managerLogin(ManagementLoginRequestModel requestModel) {
        // 校验用户名 & 密码
        if (StringUtils.isBlank(requestModel.getUserName()) || StringUtils.isBlank(requestModel.getPassWord())) {
            throw new BizException(LoginExceptionCodeEnum.USER_NAME_PWD_ERROR);
        }
        // 登录校验
        WebUiLoginUserInfoModel loginInfo;

        //平台用户登录
        TUser sysUser = loginCheck(requestModel);

        ManagementLoginResponseModel responseModel = operatorToken(sysUser);
        List<Long> roleIdList = sysUserRoleMapper.selectRoleIdsByUserId(sysUser.getId());
        List<Long> resourceIdList = sysRoleResourceMapper.selectResourceIdsByRoleIdList(roleIdList);
        List<ResourceVO> resourceVOS = sysResourceMapper.selectByRoleIdList(resourceIdList);
        LinkedHashMap<Long, List<ResourceVO>> resourceMap = resourceVOS.stream().collect(Collectors.groupingBy(ResourceVO::getParentId, LinkedHashMap::new, Collectors.toList()));
        List<ResourceVO> resourceVOS1 = resourceMap.get(CommonConstant.INTEGER_ZERO);
        List<ResourceVO> resourceList = new ArrayList<>();
        Deque<ResourceVO> preResourceList = new LinkedList<>();
        for (ResourceVO resourceVO : resourceVOS1) {
            resourceList.add(resourceVO);
            preResourceList.add(resourceVO);

        }
        while (!preResourceList.isEmpty()) {
            ResourceVO poll = preResourceList.poll();
            List<ResourceVO> resourceVOS2 = resourceMap.getOrDefault(poll.getId(), Collections.emptyList());
            poll.setResourceList(resourceVOS2);
            for (ResourceVO resourceVO : resourceVOS2) {
                preResourceList.offer(resourceVO);
            }

        }

        // 返回登录信息
        responseModel.setResourceList(resourceList);
        return responseModel;
    }

    private ManagementLoginResponseModel operatorToken(TUser sysUser) {
        ManagementLoginResponseModel responseModel = new ManagementLoginResponseModel();
        // 调用 Auth 生成 token
        TokenModule tokenModule = authService.loginCreateToken(sysUser.getId(), sysUser.getUsername());
        // 生成 Token
        responseModel.setTokenModel(tokenModule);
        responseModel.setUserId(sysUser.getId());
        responseModel.setUserName(sysUser.getUsername());

        // 加载权限
        // loadApplicationPermission(loginInfo.getUserAccount(), responseModel);
        return responseModel;
    }


//    /**
//     * 后台查询用户拥有的主菜单权限
//     */
//    private void loadApplicationPermission(String userAccount, ManagementLoginResponseModel responseModel) {
//        LoadApplicationPermissionRequestModel permissionRequestModel = new LoadApplicationPermissionRequestModel();
//        permissionRequestModel.setUserAccount(userAccount);
//        LoadApplicationPermissionResponseModel permissionList = permissionService.loadApplicationPermissionByAccount(permissionRequestModel);
//        List<Long> list = permissionList.getApplicationPermissionCode();
//        responseModel.setApplicationPermissionCode(ListUtils.isNotEmpty(list) ? list : new ArrayList<>());
//    }

    /**
     * 登录校验
     *
     * @param requestModel requestModel
     * @return loginInfo
     */
    private TUser loginCheck(ManagementLoginRequestModel requestModel) {
        List<WebUiLoginUserInfoModel> loginInfos = new ArrayList<>();
        // 账号密码登录
        TUser sysUser = sysUserMapper.selectByUsername(requestModel.getUserName());

        if (null == sysUser) {
            throw new BizException(LoginExceptionCodeEnum.USER_NAME_PWD_ERROR);
        }
//        if (!sysUser.getPassword().equals(BcryptUtils.bcryptForPassword(requestModel.getPassWord(), sysUser.getUserSalt()))) {
//            throw new BizException(LoginExceptionCodeEnum.USER_NAME_PWD_ERROR);
//        }
        // 校验启用状态
        if (CommonConstant.INTEGER_ZERO.equals(sysUser.getStatus())) {
            throw new BizException(LoginExceptionCodeEnum.USER_HAVE_DISABLED);
        }
        return sysUser;
    }

    /**
     * 登录成功后台缓存权限菜单
     *
     * @param roles roles
     */
//    public void cacheRole(List<Long> roles) {
//        if (ListUtils.isNotEmpty(roles)) {
//
//            // 1.查询所有角色对应的菜单
//            List<RolePermissionModel> rolePermissionModelList = tUserMapper.getRolePermissionList(roles);
//            Map<Long, List<String>> rolePermissionMap = Optional.ofNullable(rolePermissionModelList).orElse(new ArrayList<>()).stream()
//                    .collect(Collectors.groupingBy(RolePermissionModel::getRoleId, Collectors.mapping(RolePermissionModel::getPermissionCode, Collectors.toList())));
//            if (CollectionUtils.isEmpty(rolePermissionMap)) {
//                return;
//            }
//
//            // 2.构建角色菜单集合
//            Map<String, List<String>> permissionMap = new HashMap<>();
//            for (Long role : roles) {
//                List<String> permissionCodes = rolePermissionMap.get(role);
//                if (CollectionUtils.isEmpty(permissionCodes)) {
//                    continue;
//                }
//                permissionMap.put(ConverterUtils.toString(role), permissionCodes);
//            }
//
//            // 3.缓存数据
//            if (!CollectionUtils.isEmpty(permissionMap)) {
//                redisTemplate.setHashKeySerializer(new StringRedisSerializer());
//                redisTemplate.opsForHash().putAll(CommonConstant.MES_MANAGEMENT_WEBAPI_PERMISSIONS_KEY, permissionMap);
//            }
//        }
//    }


}
