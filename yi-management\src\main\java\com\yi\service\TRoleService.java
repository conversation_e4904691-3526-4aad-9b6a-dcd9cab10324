package com.yi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.TRole;
import com.yi.entity.TRoleResource;
import com.yi.mapper.TRoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 角色表 服务类
 */
@Service
public class TRoleService extends ServiceImpl<TRoleMapper, TRole> {

    @Autowired
    private TRoleResourceService tRoleResourceService;

    /**
     * 分页查询角色列表
     */
    public IPage<TRole> selectRolePage(Page<TRole> page, String roleCode, String roleName, Integer status) {
        return baseMapper.selectRolePage(page, roleCode, roleName, status);
    }

    /**
     * 根据角色编码查询角色
     */
    public TRole selectByRoleCode(String roleCode) {
        return baseMapper.selectByRoleCode(roleCode);
    }

    /**
     * 根据用户ID查询角色列表
     */
    public List<TRole> selectByUserId(Long userId) {
        return baseMapper.selectByUserId(userId);
    }

    /**
     * 查询角色的资源ID列表
     */
    public List<Long> selectResourceIdsByRoleId(Long roleId) {
        return baseMapper.selectResourceIdsByRoleId(roleId);
    }

    /**
     * 查询所有启用的角色
     */
    public List<TRole> selectEnabledRoles() {
        return baseMapper.selectEnabledRoles();
    }

    /**
     * 创建角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createRole(TRole role) {
        // 检查角色编码是否存在
        if (checkRoleCodeExists(role.getRoleCode(), null)) {
            throw new RuntimeException("角色编码已存在");
        }
        
        // 设置默认值
        role.setStatus(1);
        role.setValid(1);
        role.setCreatedTime(LocalDateTime.now());
        role.setLastModifiedTime(LocalDateTime.now());
        
        return save(role);
    }

    /**
     * 更新角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(TRole role) {
        // 检查角色编码是否存在
        if (checkRoleCodeExists(role.getRoleCode(), role.getId())) {
            throw new RuntimeException("角色编码已存在");
        }
        
        role.setLastModifiedTime(LocalDateTime.now());
        return updateById(role);
    }

    /**
     * 删除角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long roleId) {
        // 删除角色资源关联
        tRoleResourceService.deleteByRoleId(roleId);
        
        // 逻辑删除角色
        TRole role = new TRole();
        role.setId(roleId);
        role.setValid(0);
        role.setLastModifiedTime(LocalDateTime.now());
        
        return updateById(role);
    }

    /**
     * 批量删除角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoles(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return true;
        }
        
        for (Long roleId : roleIds) {
            deleteRole(roleId);
        }
        return true;
    }

    /**
     * 分配角色权限
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean assignPermissions(Long roleId, List<Long> resourceIds) {
        // 先删除原有权限
        tRoleResourceService.deleteByRoleId(roleId);
        
        // 分配新权限
        if (!CollectionUtils.isEmpty(resourceIds)) {
            List<TRoleResource> roleResources = new ArrayList<>();
            for (Long resourceId : resourceIds) {
                TRoleResource roleResource = new TRoleResource();
                roleResource.setRoleId(roleId);
                roleResource.setResourceId(resourceId);
                roleResource.setCreatedTime(LocalDateTime.now());
                roleResources.add(roleResource);
            }
            tRoleResourceService.saveBatch(roleResources);
        }
        
        return true;
    }

    /**
     * 启用/禁用角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long roleId, Integer status) {
        TRole role = new TRole();
        role.setId(roleId);
        role.setStatus(status);
        role.setLastModifiedTime(LocalDateTime.now());
        return updateById(role);
    }

    /**
     * 检查角色编码是否存在
     */
    public boolean checkRoleCodeExists(String roleCode, Long excludeId) {
        LambdaQueryWrapper<TRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TRole::getRoleCode, roleCode)
               .eq(TRole::getValid, 1);
        if (excludeId != null) {
            wrapper.ne(TRole::getId, excludeId);
        }
        return count(wrapper) > 0;
    }
}
