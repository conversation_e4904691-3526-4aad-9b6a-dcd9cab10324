package com.yi.configuration.exception;

import com.yi.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BizException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result<Object> handleBizException(BizException e, HttpServletRequest request) {
        log.warn("业务异常: {} - {}", e.getCode(), e.getMessage());

        // 如果是认证相关异常，返回401状态码
        if (isAuthException(e.getCode())) {
            return Result.unauthorized(null);
        }

        return Result.failed(e.getCode(), e.getMessage());
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Object> handleException(Exception e, HttpServletRequest request) {
        log.error("系统异常: ", e);
        return Result.failed(500, "系统内部错误");
    }

    /**
     * 判断是否为认证相关异常
     */
    private boolean isAuthException(int code) {
        return code >= 4001 && code <= 4042;
    }
}
