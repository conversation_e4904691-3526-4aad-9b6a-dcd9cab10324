import request from '@/utils/request'

// 客户管理API
export const customerApi = {
  // 分页查询客户列表
  getCustomerPage(params) {
    return request({
      url: '/api/customer-company/page',
      method: 'post',
      data: params
    })
  },

  // 根据ID获取客户详情
  getCustomerById(id) {
    return request({
      url: `/api/customer-company/${id}`,
      method: 'get'
    })
  },

  // 新增客户
  addCustomer(data) {
    return request({
      url: '/api/customer-company/add',
      method: 'post',
      data
    })
  },

  // 更新客户
  updateCustomer(data) {
    return request({
      url: '/api/customer-company/update',
      method: 'put',
      data
    })
  },

  // 更新客户状态
  updateCustomerStatus(id, enabled) {
    return request({
      url: `/api/customer-company/${id}/status`,
      method: 'put',
      params: { enabled }
    })
  },

  // 根据类型查询客户
  getCustomerByType(customerCompanyType) {
    return request({
      url: '/api/customer-company/by-type',
      method: 'get',
      params: { customerCompanyType }
    })
  },

  // 导出客户列表
  exportCustomerList(params) {
    return request({
      url: '/api/customer-company/export',
      method: 'post',
      data: params,
      responseType: 'blob'
    })
  }
}

export default customerApi
