-- =============================================
-- RBAC权限管理系统初始化数据
-- =============================================

-- 1. 初始化角色数据
INSERT INTO `t_role` (`id`, `role_code`, `role_name`, `role_desc`, `role_sort`, `data_scope`, `status`, `created_by`, `created_time`) VALUES
(1, 'SUPER_ADMIN', '超级管理员', '拥有系统所有权限', 1, 1, 1, 'system', NOW()),
(2, 'ADMIN', '系统管理员', '拥有系统管理权限', 2, 2, 1, 'system', NOW()),
(3, 'USER_MANAGER', '用户管理员', '用户管理相关权限', 3, 3, 1, 'system', NOW()),
(4, 'BUSINESS_USER', '业务用户', '业务操作权限', 4, 4, 1, 'system', NOW()),
(5, 'GUEST', '访客', '只读权限', 5, 5, 1, 'system', NOW());

-- 2. 初始化用户数据（密码为123456的MD5加密：e10adc3949ba59abbe56e057f20f883e）
INSERT INTO `t_user` (`id`, `username`, `password`, `real_name`, `email`, `phone`, `gender`, `status`, `created_by`, `created_time`) VALUES
(1, 'admin', 'e10adc3949ba59abbe56e057f20f883e', '系统管理员', '<EMAIL>', '***********', 1, 1, 'system', NOW()),
(2, 'user_manager', 'e10adc3949ba59abbe56e057f20f883e', '用户管理员', '<EMAIL>', '***********', 1, 1, 'system', NOW()),
(3, 'business_user', 'e10adc3949ba59abbe56e057f20f883e', '业务用户', '<EMAIL>', '***********', 0, 1, 'system', NOW()),
(4, 'guest', 'e10adc3949ba59abbe56e057f20f883e', '访客用户', '<EMAIL>', '***********', 1, 1, 'system', NOW());

-- 3. 初始化资源数据（菜单权限）
INSERT INTO `t_resource` (`id`, `parent_id`, `resource_code`, `resource_name`, `resource_type`, `path`, `component`, `perms`, `icon`, `order_num`, `visible`, `status`, `created_by`, `created_time`) VALUES
-- 一级菜单
(1, 0, 'SYSTEM', '系统管理', 1, '/system', NULL, NULL, 'system', 1, 1, 1, 'system', NOW()),
(2, 0, 'BUSINESS', '业务管理', 1, '/business', NULL, NULL, 'business', 2, 1, 1, 'system', NOW()),
(3, 0, 'MONITOR', '系统监控', 1, '/monitor', NULL, NULL, 'monitor', 3, 1, 1, 'system', NOW()),

-- 系统管理子菜单
(10, 1, 'USER_MANAGE', '用户管理', 2, '/system/user', 'system/user/index', 'system:user:list', 'user', 1, 1, 1, 'system', NOW()),
(11, 1, 'ROLE_MANAGE', '角色管理', 2, '/system/role', 'system/role/index', 'system:role:list', 'peoples', 2, 1, 1, 'system', NOW()),
(12, 1, 'RESOURCE_MANAGE', '菜单管理', 2, '/system/resource', 'system/resource/index', 'system:resource:list', 'tree-table', 3, 1, 1, 'system', NOW()),


-- 用户管理按钮权限
(100, 10, 'USER_ADD', '用户新增', 3, NULL, NULL, 'system:user:add', NULL, 1, 1, 1, 'system', NOW()),
(101, 10, 'USER_EDIT', '用户修改', 3, NULL, NULL, 'system:user:edit', NULL, 2, 1, 1, 'system', NOW()),
(102, 10, 'USER_DELETE', '用户删除', 3, NULL, NULL, 'system:user:delete', NULL, 3, 1, 1, 'system', NOW()),
(103, 10, 'USER_EXPORT', '用户导出', 3, NULL, NULL, 'system:user:export', NULL, 4, 1, 1, 'system', NOW()),
(104, 10, 'USER_IMPORT', '用户导入', 3, NULL, NULL, 'system:user:import', NULL, 5, 1, 1, 'system', NOW()),
(105, 10, 'USER_RESET_PWD', '重置密码', 3, NULL, NULL, 'system:user:resetPwd', NULL, 6, 1, 1, 'system', NOW()),

-- 角色管理按钮权限
(110, 11, 'ROLE_ADD', '角色新增', 3, NULL, NULL, 'system:role:add', NULL, 1, 1, 1, 'system', NOW()),
(111, 11, 'ROLE_EDIT', '角色修改', 3, NULL, NULL, 'system:role:edit', NULL, 2, 1, 1, 'system', NOW()),
(112, 11, 'ROLE_DELETE', '角色删除', 3, NULL, NULL, 'system:role:delete', NULL, 3, 1, 1, 'system', NOW()),
(113, 11, 'ROLE_EXPORT', '角色导出', 3, NULL, NULL, 'system:role:export', NULL, 4, 1, 1, 'system', NOW()),
(114, 11, 'ROLE_AUTH', '分配权限', 3, NULL, NULL, 'system:role:auth', NULL, 5, 1, 1, 'system', NOW()),

-- 菜单管理按钮权限
(120, 12, 'RESOURCE_ADD', '菜单新增', 3, NULL, NULL, 'system:resource:add', NULL, 1, 1, 1, 'system', NOW()),
(121, 12, 'RESOURCE_EDIT', '菜单修改', 3, NULL, NULL, 'system:resource:edit', NULL, 2, 1, 1, 'system', NOW()),
(122, 12, 'RESOURCE_DELETE', '菜单删除', 3, NULL, NULL, 'system:resource:delete', NULL, 3, 1, 1, 'system', NOW()),



-- 业务管理菜单
(200, 2, 'WAREHOUSE_MANAGE', '仓库管理', 2, '/business/warehouse', 'business/warehouse/index', 'business:warehouse:list', 'warehouse', 1, 1, 1, 'system', NOW()),
(201, 2, 'SKU_MANAGE', 'SKU管理', 2, '/business/sku', 'business/sku/index', 'business:sku:list', 'sku', 2, 1, 1, 'system', NOW()),
(202, 2, 'COMPANY_MANAGE', '公司管理', 2, '/business/company', 'business/company/index', 'business:company:list', 'company', 3, 1, 1, 'system', NOW());

-- 4. 初始化用户角色关联
INSERT INTO `t_user_role` (`user_id`, `role_id`, `created_by`, `created_time`) VALUES
(1, 1, 'system', NOW()), -- admin用户分配超级管理员角色
(2, 3, 'system', NOW()), -- user_manager用户分配用户管理员角色
(3, 4, 'system', NOW()), -- business_user用户分配业务用户角色
(4, 5, 'system', NOW()); -- guest用户分配访客角色

-- 5. 初始化角色资源关联（超级管理员拥有所有权限）
INSERT INTO `t_role_resource` (`role_id`, `resource_id`, `created_by`, `created_time`)
SELECT 1, id, 'system', NOW() FROM `t_resource` WHERE `valid` = 1;

-- 用户管理员权限
INSERT INTO `t_role_resource` (`role_id`, `resource_id`, `created_by`, `created_time`) VALUES
(3, 1, 'system', NOW()),   -- 系统管理目录
(3, 10, 'system', NOW()),  -- 用户管理菜单
(3, 100, 'system', NOW()), -- 用户新增
(3, 101, 'system', NOW()), -- 用户修改
(3, 102, 'system', NOW()), -- 用户删除
(3, 103, 'system', NOW()), -- 用户导出
(3, 105, 'system', NOW()); -- 重置密码

-- 业务用户权限
INSERT INTO `t_role_resource` (`role_id`, `resource_id`, `created_by`, `created_time`) VALUES
(4, 2, 'system', NOW()),   -- 业务管理目录
(4, 200, 'system', NOW()), -- 仓库管理
(4, 201, 'system', NOW()), -- SKU管理
(4, 202, 'system', NOW()); -- 公司管理

-- 访客权限（只读）
INSERT INTO `t_role_resource` (`role_id`, `resource_id`, `created_by`, `created_time`) VALUES
(5, 2, 'system', NOW()),   -- 业务管理目录
(5, 200, 'system', NOW()), -- 仓库管理（只读）
(5, 201, 'system', NOW()), -- SKU管理（只读）
(5, 202, 'system', NOW()); -- 公司管理（只读）
