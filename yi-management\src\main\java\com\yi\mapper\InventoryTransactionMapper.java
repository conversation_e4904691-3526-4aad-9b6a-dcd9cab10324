package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.InventoryTransaction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 库存流水表 Mapper 接口
 */
@Mapper
public interface InventoryTransactionMapper extends BaseMapper<InventoryTransaction> {

    /**
     * 分页查询库存流水列表
     *
     * @param page 分页参数
     * @param transactionType 交易类型
     * @param orderId 订单ID
     * @param orderNo 订单号
     * @param warehouseId 仓库ID
     * @param firstCategory 一级类目
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<InventoryTransaction> selectTransactionPage(Page<InventoryTransaction> page,
                                                      @Param("transactionType") Integer transactionType,
                                                      @Param("orderId") Long orderId,
                                                      @Param("orderNo") String orderNo,
                                                      @Param("warehouseId") Long warehouseId,
                                                      @Param("firstCategory") Integer firstCategory,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 根据订单ID查询流水列表
     *
     * @param orderId 订单ID
     * @return 流水列表
     */
    List<InventoryTransaction> selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单号查询流水列表
     *
     * @param orderNo 订单号
     * @return 流水列表
     */
    List<InventoryTransaction> selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据明细ID查询流水列表
     *
     * @param detailId 明细ID
     * @return 流水列表
     */
    List<InventoryTransaction> selectByDetailId(@Param("detailId") Long detailId);

    /**
     * 查询仓库库存汇总
     *
     * @param warehouseId 仓库ID
     * @param firstCategory 一级类目
     * @param secondCategory 二级类目
     * @return 库存汇总
     */
    Map<String, Object> selectInventorySummary(@Param("warehouseId") Long warehouseId,
                                               @Param("firstCategory") Integer firstCategory,
                                               @Param("secondCategory") String secondCategory);

    /**
     * 查询所有仓库库存汇总
     *
     * @return 库存汇总列表
     */
    List<Map<String, Object>> selectAllInventorySummary();

    /**
     * 查询库存变化趋势
     *
     * @param warehouseId 仓库ID
     * @param firstCategory 一级类目
     * @param secondCategory 二级类目
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 变化趋势数据
     */
    List<Map<String, Object>> selectInventoryTrend(@Param("warehouseId") Long warehouseId,
                                                   @Param("firstCategory") Integer firstCategory,
                                                   @Param("secondCategory") String secondCategory,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询当前库存数量
     *
     * @param warehouseId 仓库ID
     * @param firstCategory 一级类目
     * @param secondCategory 二级类目
     * @return 当前库存数量
     */
    Integer selectCurrentStock(@Param("warehouseId") Long warehouseId,
                               @Param("firstCategory") Integer firstCategory,
                               @Param("secondCategory") String secondCategory);

    /**
     * 批量插入库存流水
     *
     * @param transactions 流水列表
     * @return 插入行数
     */
    int batchInsert(@Param("transactions") List<InventoryTransaction> transactions);

    /**
     * 统计交易类型数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> selectTransactionTypeStatistics(@Param("startTime") LocalDateTime startTime,
                                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 统计仓库交易数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> selectWarehouseStatistics(@Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);
}
