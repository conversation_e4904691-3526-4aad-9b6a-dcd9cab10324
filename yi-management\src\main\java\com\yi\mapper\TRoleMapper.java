package com.yi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yi.entity.TRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色表 Mapper 接口
 */
@Mapper
public interface TRoleMapper extends BaseMapper<TRole> {

    /**
     * 分页查询角色列表
     *
     * @param page 分页参数
     * @param roleCode 角色编码（模糊查询）
     * @param roleName 角色名称（模糊查询）
     * @param status 状态
     * @return 分页结果
     */
    IPage<TRole> selectRolePage(Page<TRole> page,
                                @Param("roleCode") String roleCode,
                                @Param("roleName") String roleName,
                                @Param("status") Integer status);

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    TRole selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<TRole> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询角色的资源ID列表
     *
     * @param roleId 角色ID
     * @return 资源ID列表
     */
    List<Long> selectResourceIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 查询所有启用的角色
     *
     * @return 角色列表
     */
    List<TRole> selectEnabledRoles();
}
