<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yi.mapper.TUserRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yi.entity.TUserRole">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="last_modified_by" property="lastModifiedBy" />
        <result column="last_modified_time" property="lastModifiedTime" />
        <result column="valid" property="valid" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, role_id, created_by, created_time, last_modified_by, last_modified_time, valid
    </sql>

    <!-- 根据用户ID删除用户角色关联 -->
    <delete id="deleteByUserId">
        UPDATE t_user_role 
        SET valid = 0, last_modified_time = NOW()
        WHERE user_id = #{userId} AND valid = 1
    </delete>

    <!-- 根据角色ID删除用户角色关联 -->
    <delete id="deleteByRoleId">
        UPDATE t_user_role 
        SET valid = 0, last_modified_time = NOW()
        WHERE role_id = #{roleId} AND valid = 1
    </delete>

    <!-- 批量插入用户角色关联 -->
    <insert id="batchInsert">
        INSERT INTO t_user_role (user_id, role_id, created_by, created_time, valid)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.roleId}, #{item.createdBy}, #{item.createdTime}, 1)
        </foreach>
    </insert>

    <!-- 根据用户ID查询角色ID列表 -->
    <select id="selectRoleIdsByUserId" resultType="java.lang.Long">
        SELECT role_id
        FROM t_user_role
        WHERE user_id = #{userId} AND valid = 1
    </select>

    <!-- 根据角色ID查询用户ID列表 -->
    <select id="selectUserIdsByRoleId" resultType="java.lang.Long">
        SELECT user_id
        FROM t_user_role
        WHERE role_id = #{roleId} AND valid = 1
    </select>

</mapper>
