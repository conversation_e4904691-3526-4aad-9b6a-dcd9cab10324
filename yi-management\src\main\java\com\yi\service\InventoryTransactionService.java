package com.yi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yi.entity.InventoryTransaction;
import com.yi.mapper.InventoryTransactionMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 库存流水表 服务类
 */
@Service
public class InventoryTransactionService extends ServiceImpl<InventoryTransactionMapper, InventoryTransaction> {

    /**
     * 分页查询库存流水列表
     */
    public IPage<InventoryTransaction> selectTransactionPage(Page<InventoryTransaction> page,
                                                             Integer transactionType, Long orderId, String orderNo,
                                                             Long warehouseId, Integer firstCategory,
                                                             LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectTransactionPage(page, transactionType, orderId, orderNo, warehouseId,
                firstCategory, startTime, endTime);
    }

    /**
     * 根据订单ID查询流水列表
     */
    public List<InventoryTransaction> selectByOrderId(Long orderId) {
        return baseMapper.selectByOrderId(orderId);
    }

    /**
     * 根据订单号查询流水列表
     */
    public List<InventoryTransaction> selectByOrderNo(String orderNo) {
        return baseMapper.selectByOrderNo(orderNo);
    }

    /**
     * 根据明细ID查询流水列表
     */
    public List<InventoryTransaction> selectByDetailId(Long detailId) {
        return baseMapper.selectByDetailId(detailId);
    }

    /**
     * 查询仓库库存汇总
     */
    public Map<String, Object> getInventorySummary(Long warehouseId, Integer firstCategory, String secondCategory) {
        return baseMapper.selectInventorySummary(warehouseId, firstCategory, secondCategory);
    }

    /**
     * 查询所有仓库库存汇总
     */
    public List<Map<String, Object>> getAllInventorySummary() {
        return baseMapper.selectAllInventorySummary();
    }

    /**
     * 查询库存变化趋势
     */
    public List<Map<String, Object>> getInventoryTrend(Long warehouseId, Integer firstCategory, String secondCategory,
                                                       LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectInventoryTrend(warehouseId, firstCategory, secondCategory, startTime, endTime);
    }

    /**
     * 查询当前库存数量
     */
    public Integer getCurrentStock(Long warehouseId, Integer firstCategory, String secondCategory) {
        return baseMapper.selectCurrentStock(warehouseId, firstCategory, secondCategory);
    }

    /**
     * 批量插入库存流水
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchInsertTransactions(List<InventoryTransaction> transactions) {
        if (CollectionUtils.isEmpty(transactions)) {
            return true;
        }

        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        for (InventoryTransaction transaction : transactions) {
            if (transaction.getCreatedTime() == null) {
                transaction.setCreatedTime(now);
            }
            if (transaction.getTransactionTime() == null) {
                transaction.setTransactionTime(now);
            }
        }

        return baseMapper.batchInsert(transactions) > 0;
    }

    /**
     * 统计交易类型数量
     */
    public List<Map<String, Object>> getTransactionTypeStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectTransactionTypeStatistics(startTime, endTime);
    }

    /**
     * 统计仓库交易数量
     */
    public List<Map<String, Object>> getWarehouseStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectWarehouseStatistics(startTime, endTime);
    }

    /**
     * 创建入库流水记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createInboundTransaction(Long orderId, String orderNo, Long detailId, Long warehouseId,
                                            String warehouseName, Integer firstCategory, String secondCategory,
                                            Integer quantityBefore, Integer quantityChange, String createdBy, String remark) {
        InventoryTransaction transaction = new InventoryTransaction();
        transaction.setTransactionType(1); // 1-入库
        transaction.setOrderId(orderId);
        transaction.setOrderNo(orderNo);
        transaction.setDetailId(detailId);
        transaction.setWarehouseId(warehouseId);
        transaction.setWarehouseName(warehouseName);
        transaction.setFirstCategory(firstCategory);
        transaction.setSecondCategory(secondCategory);
        transaction.setQuantityBefore(quantityBefore);
        transaction.setQuantityChange(quantityChange);
        transaction.setQuantityAfter(quantityBefore + quantityChange);
        transaction.setTransactionTime(LocalDateTime.now());
        transaction.setCreatedBy(createdBy);
        transaction.setCreatedTime(LocalDateTime.now());
        transaction.setRemark(remark);

        return save(transaction);
    }

    /**
     * 创建出库流水记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createOutboundTransaction(Long orderId, String orderNo, Long detailId, Long warehouseId,
                                             String warehouseName, Integer firstCategory, String secondCategory,
                                             Integer quantityBefore, Integer quantityChange, String createdBy, String remark) {
        InventoryTransaction transaction = new InventoryTransaction();
        transaction.setTransactionType(2); // 2-出库
        transaction.setOrderId(orderId);
        transaction.setOrderNo(orderNo);
        transaction.setDetailId(detailId);
        transaction.setWarehouseId(warehouseId);
        transaction.setWarehouseName(warehouseName);
        transaction.setFirstCategory(firstCategory);
        transaction.setSecondCategory(secondCategory);
        transaction.setQuantityBefore(quantityBefore);
        transaction.setQuantityChange(-Math.abs(quantityChange)); // 出库为负数
        transaction.setQuantityAfter(quantityBefore - Math.abs(quantityChange));
        transaction.setTransactionTime(LocalDateTime.now());
        transaction.setCreatedBy(createdBy);
        transaction.setCreatedTime(LocalDateTime.now());
        transaction.setRemark(remark);

        return save(transaction);
    }

    /**
     * 创建调拨流水记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createTransferTransaction(Long orderId, String orderNo, Long detailId, Long warehouseId,
                                             String warehouseName, Integer firstCategory, String secondCategory,
                                             Integer quantityBefore, Integer quantityChange, String createdBy, String remark) {
        InventoryTransaction transaction = new InventoryTransaction();
        transaction.setTransactionType(3); // 3-调拨
        transaction.setOrderId(orderId);
        transaction.setOrderNo(orderNo);
        transaction.setDetailId(detailId);
        transaction.setWarehouseId(warehouseId);
        transaction.setWarehouseName(warehouseName);
        transaction.setFirstCategory(firstCategory);
        transaction.setSecondCategory(secondCategory);
        transaction.setQuantityBefore(quantityBefore);
        transaction.setQuantityChange(quantityChange);
        transaction.setQuantityAfter(quantityBefore + quantityChange);
        transaction.setTransactionTime(LocalDateTime.now());
        transaction.setCreatedBy(createdBy);
        transaction.setCreatedTime(LocalDateTime.now());
        transaction.setRemark(remark);

        return save(transaction);
    }

    /**
     * 创建盘点流水记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createInventoryCheckTransaction(Long orderId, String orderNo, Long detailId, Long warehouseId,
                                                   String warehouseName, Integer firstCategory, String secondCategory,
                                                   Integer quantityBefore, Integer quantityChange, String createdBy, String remark) {
        InventoryTransaction transaction = new InventoryTransaction();
        transaction.setTransactionType(4); // 4-盘点
        transaction.setOrderId(orderId);
        transaction.setOrderNo(orderNo);
        transaction.setDetailId(detailId);
        transaction.setWarehouseId(warehouseId);
        transaction.setWarehouseName(warehouseName);
        transaction.setFirstCategory(firstCategory);
        transaction.setSecondCategory(secondCategory);
        transaction.setQuantityBefore(quantityBefore);
        transaction.setQuantityChange(quantityChange);
        transaction.setQuantityAfter(quantityBefore + quantityChange);
        transaction.setTransactionTime(LocalDateTime.now());
        transaction.setCreatedBy(createdBy);
        transaction.setCreatedTime(LocalDateTime.now());
        transaction.setRemark(remark);

        return save(transaction);
    }

    /**
     * 根据出库单明细创建库存流水
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createTransactionFromOutboundDetail(Long outboundOrderId, String outboundOrderNo, Long detailId,
                                                       Long warehouseId, String warehouseName, Integer firstCategory,
                                                       String secondCategory, Integer actualQuantity, String createdBy) {
        // 获取当前库存
        Integer currentStock = getCurrentStock(warehouseId, firstCategory, secondCategory);
        currentStock = currentStock != null ? currentStock : 0;

        return createOutboundTransaction(outboundOrderId, outboundOrderNo, detailId, warehouseId, warehouseName,
                firstCategory, secondCategory, currentStock, actualQuantity, createdBy, "出库操作");
    }

    /**
     * 根据入库单明细创建库存流水
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createTransactionFromInboundDetail(Long inboundOrderId, String inboundOrderNo, Long detailId,
                                                      Long warehouseId, String warehouseName, Integer firstCategory,
                                                      String secondCategory, Integer actualQuantity, String createdBy) {
        // 获取当前库存
        Integer currentStock = getCurrentStock(warehouseId, firstCategory, secondCategory);
        currentStock = currentStock != null ? currentStock : 0;

        return createInboundTransaction(inboundOrderId, inboundOrderNo, detailId, warehouseId, warehouseName,
                firstCategory, secondCategory, currentStock, actualQuantity, createdBy, "入库操作");
    }
}
